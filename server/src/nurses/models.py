from typing import Optional, List
from sqlmodel import SQLModel, Field, Relationship
import enum
import json

from src.users.models import User, UserCreate, UserRead


class NurseGender(enum.StrEnum):
    MALE = "男"
    FEMALE = "女"


class NurseCore(SQLModel):
    name: Optional[str] = Field(default=None, index=True, max_length=50)
    gender: Optional[NurseGender] = Field(default=None)
    title: Optional[str] = Field(default=None, max_length=100)  # 职称
    department: Optional[str] = Field(default=None, max_length=100)  # 科室
    license_number: Optional[str] = Field(default=None, max_length=50)  # 护士执业证号
    years_of_experience: Optional[int] = Field(default=0)  # 工作年限
    specialties: Optional[str] = Field(default=None, max_length=200)  # 专业特长
    license_documents: Optional[str] = Field(default=None)  # JSON string storing document URLs
    is_active: Optional[bool] = Field(default=False)


class NurseBase(NurseCore):
    name: str = Field(index=True, max_length=50)
    gender: NurseGender
    department: str = Field(max_length=100)
    license_number: str = Field(max_length=50)


class NurseCreate(UserCreate, NurseBase):
    pass


class NurseRead(NurseBase):
    id: int
    user_id: int


class NurseUpdate(NurseCore):
    # All fields are optional for PATCH operations
    pass


class NurseWithUser(NurseRead):
    user: UserRead
    documents: Optional[List[str]] = None

    @classmethod
    def from_nurse_with_user(cls, nurse: "Nurse", user: UserRead):
        """Create NurseWithUser from Nurse instance"""
        return cls(
            id=nurse.id,
            user_id=nurse.user_id,
            name=nurse.name,
            gender=nurse.gender,
            title=nurse.title,
            department=nurse.department,
            license_number=nurse.license_number,
            years_of_experience=nurse.years_of_experience,
            specialties=nurse.specialties,
            license_documents=nurse.license_documents,
            is_active=nurse.is_active,
            user=user,
            documents=nurse.get_license_documents()
        )


class Nurse(NurseBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    # 1:1 relationship with User
    user_id: int = Field(foreign_key="user.id", unique=True)
    # Relationship back to User
    user: User = Relationship(back_populates="nurse")

    def get_license_documents(self) -> List[str]:
        """Get license documents as a list of URLs"""
        if not self.license_documents:
            return []
        try:
            return json.loads(self.license_documents)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_license_documents(self, documents: List[str]) -> None:
        """Set license documents from a list of URLs"""
        if documents:
            self.license_documents = json.dumps(documents)
        else:
            self.license_documents = None

    def add_license_document(self, document_url: str) -> None:
        """Add a new license document URL"""
        documents = self.get_license_documents()
        if document_url not in documents:
            documents.append(document_url)
            self.set_license_documents(documents)

    def remove_license_document(self, document_url: str) -> None:
        """Remove a license document URL"""
        documents = self.get_license_documents()
        if document_url in documents:
            documents.remove(document_url)
            self.set_license_documents(documents)
