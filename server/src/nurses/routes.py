from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, UploadFile, File, status
from pathlib import Path
import uuid
from pydantic import BaseModel

from src.nurses.models import Nurse, Nurse<PERSON><PERSON>, NurseRead, NurseUpdate, NurseWithUser
from src.nurses.service import NurseService

from src.auth.auth import CurrentUser
from src.db.session import DBSession

import logging

nurse_router = APIRouter(prefix="/nurses", tags=["nurses"])

logger = logging.getLogger(__name__)


@nurse_router.post("/", response_model=NurseWithUser)
async def create_nurse(
    nurse_data: NurseCreate,
    session: DBSession,
    current_user: CurrentUser
):
    """Create a new nurse (Admin only)"""
    nurse = await NurseService(session).create_nurse_with_user(nurse_data)
    return nurse


@nurse_router.get("/", response_model=List[NurseWithUser])
async def get_nurses(
    session: DBSession,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    department: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """Get list of nurses with optional filtering"""
    nurses = await NurseService(session).get_nurses(
        skip=skip, 
        limit=limit,
        department=department,
        is_active=is_active
    )
    return nurses


@nurse_router.get("/available", response_model=List[NurseWithUser])
async def get_available_nurses(
    session: DBSession,
    department: Optional[str] = Query(None)
):
    """Get available nurses for assignment"""
    nurses = await NurseService(session).get_available_nurses(
        department=department
    )
    return nurses


@nurse_router.get("/search", response_model=List[NurseWithUser])
async def search_nurses(
    session: DBSession,
    q: str = Query(..., min_length=1)
):
    """Search nurses by name or employee ID"""
    nurses = await NurseService(session).search_nurses(q)
    return nurses


@nurse_router.get("/department/{department}", response_model=List[NurseWithUser])
async def get_nurses_by_department(
    department: str,
    session: DBSession
):
    """Get all active nurses in a specific department"""
    nurses = await NurseService(session).get_nurses_by_department(department)
    return nurses


@nurse_router.get("/me", response_model=NurseWithUser)
async def get_current_nurse_profile(
    session: DBSession,
    current_user: CurrentUser
):
    """Get current user's nurse profile"""
    logger.info(f"Getting nurse profile for user {current_user.id}")
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")
    return nurse


@nurse_router.post("/me", response_model=NurseWithUser)
async def create_current_nurse_profile(
    nurse_data: NurseCreate,
    session: DBSession,
    current_user: CurrentUser
):
    """Create nurse profile for current user"""
    logger.info(f"Creating nurse profile for user {current_user.id}")
    logger.info(f"Received nurse data: {nurse_data.model_dump()}")

    # Log the current user details
    logger.info(f"Current user: {current_user.id}, openid: {getattr(current_user, 'openid', 'N/A')}")

    # Override the openid and session_key with current user's data if available
    if hasattr(current_user, 'openid') and current_user.openid:
        nurse_data.openid = current_user.openid
        logger.info(f"Set nurse_data.openid to {current_user.openid}")
    if hasattr(current_user, 'session_key') and current_user.session_key:
        nurse_data.session_key = current_user.session_key
        logger.info(f"Set nurse_data.session_key")

    try:
        logger.info(f"Calling create_nurse_profile_for_user with user_id={current_user.id}")
        nurse = await NurseService(session).create_nurse_profile_for_user(current_user.id, nurse_data)
        logger.info(f"Successfully created nurse profile for user {current_user.id}")
        return nurse
    except ValueError as e:
        logger.error(f"Validation error creating nurse profile: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating nurse profile: {e}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail="Failed to create nurse profile")


@nurse_router.patch("/me", response_model=NurseWithUser)
async def update_current_nurse_profile(
    nurse_update: NurseUpdate,
    session: DBSession,
    current_user: CurrentUser
):
    """Update current user's nurse profile"""
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    updated_nurse = await NurseService(session).update_nurse(nurse.id, nurse_update)
    return updated_nurse


@nurse_router.get("/{nurse_id}", response_model=NurseWithUser)
async def get_nurse(
    nurse_id: int,
    session: DBSession
):
    """Get a specific nurse by ID"""
    nurse = await NurseService(session).get_nurse_by_nurseid(nurse_id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.patch("/{nurse_id}", response_model=NurseWithUser)
async def update_nurse(
    nurse_id: int,
    nurse_update: NurseUpdate,
    session: DBSession
):
    """Update a nurse (Admin only)"""
    nurse = await NurseService(session).update_nurse(nurse_id, nurse_update)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.delete("/{nurse_id}")
async def delete_nurse(
    nurse_id: int,
    session: DBSession
):
    """Soft delete a nurse (Admin only)"""
    success = await NurseService(session).delete_nurse(nurse_id)
    if not success:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return {"message": "Nurse deleted successfully"}


@nurse_router.post("/upload-document", response_model=dict)
async def upload_license_document(
    file: UploadFile = File(...),
    current_user: CurrentUser = None
):
    """
    Upload a license document file and return the URL.
    Supports PDF, JPG, PNG formats for license certificates.
    """
    # Check file type - allow documents and images
    allowed_content_types = [
        'application/pdf',
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
    ]

    if not file.content_type or file.content_type not in allowed_content_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be a PDF or image (JPG, PNG, GIF, WebP)"
        )

    # Check file size (limit to 10MB for documents)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size must be less than 10MB"
        )

    # Generate unique filename
    file_extension = Path(file.filename).suffix.lower()
    allowed_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.webp']

    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported file format. Use PDF, JPG, PNG, GIF, or WebP"
        )

    unique_filename = f"{uuid.uuid4()}{file_extension}"

    # Create upload directory if it doesn't exist
    upload_dir = Path("static/uploads/documents")
    upload_dir.mkdir(parents=True, exist_ok=True)

    # Save file
    file_path = upload_dir / unique_filename
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
    except Exception as e:
        logger.error(f"Failed to save document file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )

    # Return the URL
    document_url = f"/static/uploads/documents/{unique_filename}"
    logger.info(f"Document uploaded successfully: {document_url}")
    return {"url": document_url}


class DocumentRequest(BaseModel):
    document_url: str

@nurse_router.post("/me/documents", response_model=dict)
async def add_license_document(
    request: DocumentRequest,
    session: DBSession,
    current_user: CurrentUser
):
    """Add a license document URL to current nurse's profile"""
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    # Get the actual nurse record to update
    nurse_record = await session.get(Nurse, nurse.id)
    if not nurse_record:
        raise HTTPException(status_code=404, detail="Nurse record not found")

    # Add the document URL
    nurse_record.add_license_document(request.document_url)
    session.add(nurse_record)
    await session.commit()

    return {
        "message": "Document added successfully",
        "documents": nurse_record.get_license_documents()
    }


@nurse_router.delete("/me/documents")
async def remove_license_document(
    request: DocumentRequest,
    session: DBSession,
    current_user: CurrentUser
):
    """Remove a license document URL from current nurse's profile"""
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    # Get the actual nurse record to update
    nurse_record = await session.get(Nurse, nurse.id)
    if not nurse_record:
        raise HTTPException(status_code=404, detail="Nurse record not found")

    # Remove the document URL
    nurse_record.remove_license_document(request.document_url)
    session.add(nurse_record)
    await session.commit()

    return {
        "message": "Document removed successfully",
        "documents": nurse_record.get_license_documents()
    }
