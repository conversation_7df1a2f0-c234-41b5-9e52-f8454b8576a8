
from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import Column, DateTime, Relationship, SQLModel, Field, func


class UserBase(SQLModel):
    openid: Optional[str] = Field(default=None, index=True, unique=True)
    session_key: Optional[str] = Field(default=None)
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    phone: Optional[str] = None
    username: Optional[str] = Field(default=None, index=True, unique=True)
    hashed_password: Optional[str] = Field(default=None)
    is_active: bool = Field(default=True, nullable=False)
    is_admin: bool = Field(default=False, nullable=False)


class User(UserBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    )

    # relationship
    patients: List["Patient"] = Relationship(back_populates="user")
    orders: List["Order"] = Relationship(back_populates="user")
    nurse: Optional["Nurse"] = Relationship(back_populates="user", sa_relationship_kwargs={"uselist": False})


class UserCreate(UserBase):
    pass

class UserUpdate(SQLModel):
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    phone: Optional[str] = None
    # Password updates should be handled via a separate, dedicated endpoint for security.
    # We are not adding password to UserUpdate for this reason.
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None


class UserRead(SQLModel):
    id: int
    openid: str
    nickname: Optional[str]
    avatar: Optional[str]
    phone: Optional[str]
    is_active: bool
    is_admin: bool
    created_at: datetime
