
import random

from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import select
from src.auth.auth import CurrentUser
from src.db.session import DBSession
from src.orders.models import Order
from src.orders.wechat_pay import get_payment_mode, is_mock_mode
from src.orders.mock_pay import MockPaymentTestUtils

import logging
logger = logging.getLogger(__name__)



# Create test router (you can include this in your main router or create separately)
test_router = APIRouter(
    prefix="/mockpay",
    tags=["testing"],
    responses={404: {"description": "Not found"}},
)


@test_router.get("/payment-mode")
async def get_current_payment_mode():
    """Get current payment mode (mock or real)"""
    return {
        "mode": get_payment_mode(),
        "is_mock": is_mock_mode(),
        "description": "Mock mode is used for development when WeChat Pay credentials are not available"
    }


@test_router.get("/mock-payments")
async def list_mock_payments():
    """List all mock payments (only works in mock mode)"""
    if not is_mock_mode():
        raise HTTPException(status_code=400, detail="Not in mock payment mode")
    
    test_utils = MockPaymentTestUtils
    if not test_utils:
        raise HTTPException(status_code=500, detail="Mock test utils not available")
    
    payments = test_utils.list_all_payments()
    return {"payments": payments}


@test_router.post("/complete/{order_num}")
async def complete_mock_payment(order_num: str, session: DBSession):
    """Manually complete a mock payment for testing"""
    if not is_mock_mode():
        raise HTTPException(status_code=400, detail="Not in mock payment mode")
    
    test_utils = MockPaymentTestUtils
    if not test_utils:
        raise HTTPException(status_code=500, detail="Mock test utils not available")
    
    # Check if order exists in database
    query = select(Order).where(Order.order_num == order_num)
    result = await session.exec(query)
    order = result.first()
    
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    # Complete the mock payment
    success_rate = 0.8
    if random.random() < success_rate:
        logger.warning(f">>>>>>>>>randomly complete payment for order {order_num}")
        success = test_utils.complete_payment(order_num)
    else:
        logger.warning(f">>>>>>>>>randomly fail payment for order {order_num}")
        success = test_utils.fail_payment(order_num)
    
    if success:
        return {"message": f"Mock payment completed for order {order_num}"}
    else:
        raise HTTPException(status_code=400, detail="Failed to complete mock payment")


@test_router.post("/fail/{order_num}")
async def fail_mock_payment(order_num: str, session: DBSession):
    """Manually fail a mock payment for testing"""
    if not is_mock_mode():
        raise HTTPException(status_code=400, detail="Not in mock payment mode")
    
    test_utils = MockPaymentTestUtils
    if not test_utils:
        raise HTTPException(status_code=500, detail="Mock test utils not available")
    
    # Check if order exists
    query = select(Order).where(Order.order_num == order_num)
    result = await session.exec(query)
    order = result.first()
    
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    # Fail the mock payment
    success = test_utils.fail_payment(order_num)
    
    if success:
        return {"message": f"Mock payment failed for order {order_num}"}
    else:
        raise HTTPException(status_code=400, detail="Failed to fail mock payment")


@test_router.get("/status/{order_num}")
async def get_mock_payment_status(order_num: str):
    """Get mock payment status for testing"""
    if not is_mock_mode():
        raise HTTPException(status_code=400, detail="Not in mock payment mode")
    
    test_utils = MockPaymentTestUtils
    if not test_utils:
        raise HTTPException(status_code=500, detail="Mock test utils not available")
    
    status = test_utils.get_payment_status(order_num)
    
    if status:
        return {"order_num": order_num, "status": status}
    else:
        raise HTTPException(status_code=404, detail="Mock payment not found")
