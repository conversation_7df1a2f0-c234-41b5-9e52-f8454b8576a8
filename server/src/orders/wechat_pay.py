from wechatpayv3 import WeChatPay, WeChatPayType
import logging
from typing import Optional

from src.utils.config import settings

logger = logging.getLogger(__name__)

# Global variable for the client
payment_client: Optional[WeChatPay] = None
mock_payment = False


def use_mock() -> bool:
    if hasattr(settings, 'USE_MOCK_PAYMENT'):
        return bool(settings.USE_MOCK_PAYMENT)
   
    required_settings = [
        settings.PATIENT_APP_ID,
        settings.PAY_MCHID,
        settings.PAY_API_V3_KEY,
        settings.PAY_CERT_SERIAL_NO,
        settings.PAY_PRIVATE_KEY_PATH,
        settings.PAY_NOTIFY_URL
    ]
    
    # If any required setting is missing, use mock
    return not all(required_settings)


def initialize_client():
    """
    Initialize either real WeChat Pay client or mock client based on configuration.
    Returns the client instance or None if initialization fails.
    """
    global mock_payment
    
    # Determine which payment method to use
    mock_payment = use_mock()
    
    if mock_payment:
        logger.info("Using mock WeChat Pay for development/testing")
        try:
            from .mock_pay import initialize_mock_client
            return initialize_mock_client()
        except ImportError:
            logger.error("Mock payment module not found, falling back to real WeChat Pay")
            mock_payment = False
    
    # Initialize real WeChat Pay client
    logger.info("Initializing real WeChat Pay client")
    
    APPID = settings.PATIENT_APP_ID
    MCHID = settings.PAY_MCHID
    API_V3_KEY = settings.PAY_API_V3_KEY
    CERT_SERIAL_NO = settings.PAY_CERT_SERIAL_NO
    PRIVATE_KEY_PATH = settings.PAY_PRIVATE_KEY_PATH
    NOTIFY_URL = settings.PAY_NOTIFY_URL

    required_vars = {
        "WECHAT_MINI_APP_APPID": APPID,
        "WECHAT_PAY_MCHID": MCHID,
        "WECHAT_PAY_API_V3_KEY": API_V3_KEY,
        "WECHAT_PAY_CERT_SERIAL_NO": CERT_SERIAL_NO,
        "WECHAT_PAY_PRIVATE_KEY_PATH": PRIVATE_KEY_PATH,
        "WECHAT_PAY_NOTIFY_URL": NOTIFY_URL,
    }

    missing_vars = [key for key, value in required_vars.items() if not value]
    if missing_vars:
        logger.critical(
            f"WeChat Pay client initialization failed. Missing required environment variables: {', '.join(missing_vars)}")
        
        # Try to fallback to mock if real WeChat Pay is not configured
        if not mock_payment:
            logger.info("Falling back to mock WeChat Pay due to missing configuration")
            mock_payment = True
            try:
                from .mock_pay import initialize_mock_client
                return initialize_mock_client()
            except ImportError:
                logger.error("Mock payment fallback also failed")
                return None
        return None

    try:
        with open(PRIVATE_KEY_PATH, 'r') as f:
            private_key = f.read()

        client = WeChatPay(
            wechatpay_type=WeChatPayType.MINIPROG,
            mchid=MCHID,
            private_key=private_key,
            cert_serial_no=CERT_SERIAL_NO,
            apiv3_key=API_V3_KEY,
            appid=APPID,
            notify_url=NOTIFY_URL,
        )
        logger.info("Real WeChat Pay client initialized successfully.")
        return client
    except FileNotFoundError:
        logger.critical(
            f"WeChat Pay client initialization failed. Private key file not found: {PRIVATE_KEY_PATH}")
        
        # Try mock fallback
        if not mock_payment:
            logger.info("Falling back to mock WeChat Pay due to missing private key")
            mock_payment = True
            try:
                from .mock_pay import initialize_mock_client
                return initialize_mock_client()
            except ImportError:
                pass
        return None
    except Exception as e:
        logger.critical(f"Unexpected error during WeChat Pay client initialization: {e}", exc_info=True)
        return None


# Initialize client at module import
payment_client = initialize_client()


async def create_jsapi_payment(order_description: str, out_trade_no: str, total_fee: int, user_openid: str):
    """
    Creates a WeChat JSAPI payment order. Uses mock or real implementation based on configuration.

    :param order_description: A description of the order.
    :param out_trade_no: The unique order ID from your system.
    :param total_fee: The total fee in cents.
    :param user_openid: The user's openid for the mini-program.
    :return: A tuple of (success, payment_data_or_error_message).
    """
    if not payment_client:
        logger.error("Payment client is not initialized. Please check server startup logs.")
        return False, "Payment service is currently unavailable."

    try:
        if mock_payment:
            from .mock_pay import create_jsapi_payment as mock_create_payment
            logger.info(f"Creating mock payment for order {out_trade_no}")
            return await mock_create_payment(order_description, out_trade_no, total_fee, user_openid)
        else:
            logger.info(f"Creating WeChat payment for order {out_trade_no}")
            code, message = await payment_client.pay(
                description=order_description,
                out_trade_no=out_trade_no,
                amount={'total': total_fee},
                payer={'openid': user_openid}
            )

            if code == 200:
                return True, message
            else:
                logger.error(f"WeChat Pay API Error creating prepay order {out_trade_no}: {message}")
                return False, message.get('message', 'Failed to create payment order.')

    except Exception as e:
        logger.exception(f"Error creating payment for {out_trade_no}: {e}")
        return False, "An internal error occurred while processing payment."


async def parse_payment_notification(request_data: bytes, signature: str, timestamp: str, nonce: str, serial: str):
    """
    Parses and verifies a WeChat Pay notification.

    :param request_data: The raw request body from WeChat.
    :param signature: The 'Wechatpay-Signature' header.
    :param timestamp: The 'Wechatpay-Timestamp' header.
    :param nonce: The 'Wechatpay-Nonce' header.
    :param serial: The 'Wechatpay-Serial' header.
    :return: A tuple of (success, result).
    """
    if not payment_client:
        logger.error("Payment client is not initialized.")
        return False, "Payment service is not configured."

    try:
        if mock_payment:
            # Use mock notification parsing
            from .mock_pay import parse_payment_notification as mock_parse_notification
            logger.info("Parsing mock payment notification")
            return await mock_parse_notification(request_data, signature, timestamp, nonce, serial)
        else:
            # Use real WeChat Pay notification parsing
            logger.info("Parsing real WeChat payment notification")
            headers = {
                'Wechatpay-Signature': signature,
                'Wechatpay-Timestamp': timestamp,
                'Wechatpay-Nonce': nonce,
                'Wechatpay-Serial': serial
            }
            result = payment_client.callback(headers=headers, body=request_data)
            if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
                resp = result.get('resource')
                out_trade_no = resp.get('out_trade_no')
                logger.info(f"notification verified for event: {out_trade_no}")
                return True, result
            else:
                return False, "notification verification failed."
    except Exception as e:
        logger.error(f"Failed to verify payment notification: {e}")
        return False, "Signature verification failed."


def get_payment_mode() -> str:
    """Return current payment mode for debugging/status purposes"""
    return "mock" if mock_payment else "real"


def is_mock_mode() -> bool:
    """Check if currently using mock payment mode"""
    return mock_payment

