import logging
from decimal import Decimal
from datetime import date, datetime, timedelta, time as dt_time, timezone
from typing import List, Optional, Literal

from fastapi import APIRouter, HTTPException, status, Request, Response
from sqlalchemy.orm import selectinload
from sqlmodel import func, or_, select
from src.auth.auth import Admin<PERSON>ser, CurrentUser
from src.db.session import DBSession
from src.patients.models import Patient
from src.services.models import Service
from src.orders.models import Order, OrderCreate, OrderList, OrderRead, OrderStatus, OrderUpdate
from src.orders.service import OrderService

from .wechat_pay import create_jsapi_payment, parse_payment_notification


logger = logging.getLogger(__name__)

order_router = APIRouter(
    prefix="/orders",
    tags=["orders"],
    responses={404: {"description": "Not found"}},
)


@order_router.get("/stats/summary", response_model=dict)
async def get_order_stats(session: DBSession, admin_user: AdminUser):
    """
    Get dashboard statistics for orders.
    - Completed Orders: Orders with 'COMPLETED' status.
    - Processing Orders: Orders with 'PAID' status.
    - Today's Orders: All orders created today.
    - Total Revenue: Sum of amount for 'PAID' and 'COMPLETED' orders.
    """
    # 1. Completed Orders
    completed_orders_query = select(func.count(Order.id)).where(Order.status == OrderStatus.COMPLETED)
    completed_orders_result = await session.exec(completed_orders_query)
    completed_orders_count = completed_orders_result.one_or_none() or 0

    # 2. Processing Orders (Pending completion)
    processing_orders_query = select(func.count(Order.id)).where(Order.status == OrderStatus.PAID)
    processing_orders_result = await session.exec(processing_orders_query)
    processing_orders_count = processing_orders_result.one_or_none() or 0

    # 3. Today's Orders
    today = date.today()
    start_of_day = datetime.combine(today, dt_time.min)
    todays_orders_query = select(func.count(Order.id)).where(Order.created_at >= start_of_day)
    todays_orders_result = await session.exec(todays_orders_query)
    todays_orders_count = todays_orders_result.one_or_none() or 0

    # 4. Total Revenue
    total_revenue_query = select(func.sum(Order.amount)).where(
        Order.status.in_([OrderStatus.PAID, OrderStatus.COMPLETED])
    )
    total_revenue_result = await session.exec(total_revenue_query)
    total_revenue = total_revenue_result.one_or_none() or Decimal('0.00')

    return {
        "completed_orders": completed_orders_count,
        "processing_orders": processing_orders_count,
        "todays_orders": todays_orders_count,
        "total_revenue": float(total_revenue),
    }

@order_router.get("/stats/daily-orders", response_model=List[dict])
async def get_daily_order_stats(
    session: DBSession,
    admin_user: AdminUser,
    days: int = 30,  # Default to last 30 days
    status_filter: Optional[OrderStatus] = None,
    date_field: Literal['created_at', 'service_date'] = 'created_at'
):
    """
    Get daily order counts for the last N days.
    Can be filtered by order status.
    The date can be based on 'created_at' or 'service_date'.
    Returns list of {date: str, count: int} objects.
    """
    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)
    
    # Generate all dates in the range to ensure we have entries for days with 0 orders
    date_series = [
        start_date + timedelta(days=i) 
        for i in range(days)
    ]
    
    date_column = Order.created_at
    if date_field == 'service_date':
        date_column = Order.service_date

    # Query database for orders in this date range
    daily_counts_query = (
        select(
            func.date(date_column).label("date"),
            func.count(Order.id).label("count")
        )
        .where(func.date(date_column) >= start_date)
        .where(func.date(date_column) <= end_date)
        .group_by(func.date(date_column))
    )
    
    if status_filter:
        daily_counts_query = daily_counts_query.where(Order.status == status_filter)
    
    if date_field == 'service_date':
        daily_counts_query = daily_counts_query.where(Order.service_date.isnot(None))
    
    result = await session.exec(daily_counts_query)
    db_results = result.all()
    
    # Convert to dictionary for easy lookup
    db_data = {row.date: row.count for row in db_results}
    
    # Combine with date series to ensure all dates are represented
    response_data = [
        {
            "date": single_date.strftime("%Y-%m-%d"),
            "count": db_data.get(single_date, 0)
        }
        for single_date in date_series
    ]
    
    return response_data

@order_router.get("/stats/service-types", response_model=List[dict])
async def get_service_type_stats(session: DBSession, admin_user: AdminUser):
    """
    获取不同服务类型的订单统计
    返回格式: [{"name": "服务类型A", "value": 100}, ...]
    """

    query = (
        select(
            Service.name.label("name"),
            func.count(Order.id).label("value")
        )
        .join(Order, Order.service_id == Service.id)
        .group_by(Service.name)
        .order_by(func.count(Order.id).desc())
    )
    
    result = await session.exec(query)
    all_stats = result.all()

    if len(all_stats) <= 3:
        return [{"name": row.name, "value": row.value} for row in all_stats]

    top_3_stats = all_stats[:3]
    others_value = sum(row.value for row in all_stats[3:])

    response_data = [{"name": row.name, "value": row.value} for row in top_3_stats]
    
    if others_value > 0:
        response_data.append({"name": "others", "value": others_value})

    return response_data

@order_router.get("/stats/service-time-heatmap", response_model=List[dict])
async def get_service_time_heatmap(session: DBSession, admin_user: AdminUser):
    """
    Get service time distribution for heatmap visualization.
    Returns data in format: [{"day": "Monday", "hour": 9, "count": 5}, ...]
    Only includes business hours: Monday-Friday, 9:00 AM - 5:00 PM
    """
    # Query to get service times grouped by day of week and hour
    query = (
        select(
            # Extract day of week (0=Sunday, 1=Monday, etc.)
            func.extract('dow', Order.service_date).label('day_of_week'),
            # Extract hour from service_time
            func.extract('hour', Order.service_time).label('hour'),
            func.count(Order.id).label('count')
        )
        .where(
            Order.service_date.isnot(None),
            Order.service_time.isnot(None),
            Order.status.in_([OrderStatus.PAID, OrderStatus.COMPLETED]),
            # Filter for weekdays only (1=Monday, 5=Friday)
            func.extract('dow', Order.service_date).between(1, 5),
            # Filter for business hours (9-17)
            func.extract('hour', Order.service_time).between(9, 17)
        )
        .group_by(
            func.extract('dow', Order.service_date),
            func.extract('hour', Order.service_time)
        )
    )
    
    result = await session.exec(query)
    db_results = result.all()
    
    # Convert day numbers to names (only weekdays)
    day_names = {1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday'}
    
    # Transform data for heatmap format
    heatmap_data = []
    for row in db_results:
        if int(row.day_of_week) in day_names:  # Extra safety check
            day_name = day_names[int(row.day_of_week)]
            heatmap_data.append({
                "day": day_name,
                "hour": int(row.hour),
                "count": row.count
            })
    
    return heatmap_data

@order_router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_order(
    *, order_in: OrderCreate, session: DBSession, current_user: CurrentUser
):
    order_service = OrderService(session)
    return await order_service.create_order(order_in, current_user)


@order_router.post("/{order_id}/payments")
async def retry_payment(
    *,  order_id: int, session: DBSession, current_user: CurrentUser
):
    order_service = OrderService(session)
    return await order_service.retry_payment(order_id, current_user)


@order_router.get("/", response_model=OrderList)
async def list_orders(
    *, 
    session: DBSession, 
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 20,
    status: Optional[OrderStatus] = None,
    search: Optional[str] = None,
    sort_order: Optional[str] = None,
):
    """
    Get orders.
    - If the user is an admin, returns all orders with pagination, filtering, and sorting.
    - If the user is not an admin, returns their own orders with pagination and status filtering.
    """
    # Base queries with eager loading of all relationships
    query = select(Order).options(
        selectinload(Order.service), 
        selectinload(Order.patient),
        selectinload(Order.primary_nurse),
        selectinload(Order.secondary_nurse)
    )
    count_query = select(func.count(Order.id))
    
    if current_user.is_admin:
        # Admin can see all orders with filtering and sorting
        if status:
            query = query.where(Order.status == status)
            count_query = count_query.where(Order.status == status)
        
        if search:
            search_filter = or_(
                Order.order_num.ilike(f"%{search}%"),
                Order.patient.has(Patient.name.ilike(f"%{search}%")),
                Order.service.has(Service.name.ilike(f"%{search}%"))
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)
        
        # Apply sorting
        if sort_order:
            sort_parts = sort_order.rsplit('_', 1)
            if len(sort_parts) == 2:
                sort_field, sort_direction = sort_parts
                
                column_map = {
                    "created_at": Order.created_at,
                    "amount": Order.amount,
                    "id": Order.id,
                }
                
                sort_column = column_map.get(sort_field)
                
                if sort_column is not None and sort_direction in ['asc', 'desc']:
                    if sort_direction == "asc":
                        query = query.order_by(sort_column.asc())
                    else:
                        query = query.order_by(sort_column.desc())
                else:
                    query = query.order_by(Order.created_at.desc()) # Fallback for invalid sort
            else:
                query = query.order_by(Order.created_at.desc()) # Fallback for malformed sort
        else:
            query = query.order_by(Order.created_at.desc())
    else:
        # Regular users can only see their own orders
        query = query.where(Order.user_id == current_user.id)
        count_query = count_query.where(Order.user_id == current_user.id)
        
        if status:
            query = query.where(Order.status == status)
            count_query = count_query.where(Order.status == status)
        
        # Always sort by creation date descending for regular users
        query = query.order_by(Order.created_at.desc())
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    # Execute queries
    orders_result = await session.exec(query)
    orders = orders_result.all()
    
    count_result = await session.exec(count_query)
    total = count_result.one()
    
    return OrderList(orders=orders, total=total)


@order_router.get("/{order_id}", response_model=OrderRead)
async def get_order_detail(
    *, order_id: int, session: DBSession, current_user: CurrentUser
):
    # Allow access if user is the order owner OR assigned as a nurse
    logger.info(f"Getting order {order_id} for user {current_user.id}")

    # Need to join with nurses to check user_id
    from src.nurses.models import Nurse

    query = (
        select(Order)
        .where(
            Order.id == order_id,
            or_(
                Order.user_id == current_user.id,  # Order owner
                # Check if current user is the primary nurse's user
                select(Nurse.user_id).where(Nurse.id == Order.primary_nurse_id).scalar_subquery() == current_user.id,
                # Check if current user is the secondary nurse's user
                select(Nurse.user_id).where(Nurse.id == Order.secondary_nurse_id).scalar_subquery() == current_user.id
            )
        )
        .options(
            selectinload(Order.service),
            selectinload(Order.patient),
            selectinload(Order.primary_nurse),
            selectinload(Order.secondary_nurse),
        )
    )
    result = await session.exec(query)
    order = result.first()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    return order


@order_router.patch("/{order_id}/assign-nurses", response_model=OrderRead)
async def assign_nurses_to_order(
    order_id: int,
    assignment: OrderUpdate,
    session: DBSession,
    admin_user: AdminUser,
):
    """
    Assign or un-assign primary and secondary nurses to an order.
    This is intended for admin or staff use.
    
    To assign a nurse, provide `primary_nurse_id` and/or `secondary_nurse_id`.
    To un-assign a nurse, provide the corresponding ID as `null`.
    """
    order_service = OrderService(session)
    updated_order = await order_service.assign_nurses_to_order(
        order_id, assignment
    )
    return updated_order


@order_router.patch("/{order_id}", response_model=OrderRead)
async def update_order(
    *, 
    order_id: int, 
    order_update: OrderUpdate, 
    session: DBSession, 
    current_user: CurrentUser
):
    """
    Update an order's status.
    Supports:
    - CANCELED: For unpaid/failed orders.
    - REFUNDING: For paid orders before the service date.
    """
    order_service = OrderService(session)
    return await order_service.update_order_status_by_user(
        order_id, order_update, current_user
    )

@order_router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_order(
    *,
    order_id: int,
    session: DBSession,
    current_user: CurrentUser
):
    """
    Soft delete an order by changing its status to DELETED.
    Only orders that are COMPLETED, CANCELED, or REFUNDED can be deleted by the user.
    """
    order_service = OrderService(session)
    await order_service.delete_order(order_id, current_user)
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@order_router.post("/wechat-notify")
async def wechat_pay_notification(request: Request, session: DBSession):
    """
    Handle WeChat Pay payment notifications.
    This endpoint should match the NOTIFY_URL in your WeChat Pay configuration.
    """
    try:
        body = await request.body()
        signature = request.headers.get('Wechatpay-Signature')
        timestamp = request.headers.get('Wechatpay-Timestamp')
        nonce = request.headers.get('Wechatpay-Nonce')
        serial = request.headers.get('Wechatpay-Serial')
        
        if not all([signature, timestamp, nonce, serial]):
            logger.warning("Missing required WeChat Pay notification headers")
            return Response(status_code=400, content="Missing required headers")
        
        success, result = await parse_payment_notification(
            request_data=body,
            signature=signature,
            timestamp=timestamp,
            nonce=nonce,
            serial=serial
        )
        
        if not success:
            logger.error(f"Failed to verify WeChat notification: {result}")
            return Response(status_code=400, content="Signature verification failed")
        
        event_type = result.get('event_type')
        resource = result.get('resource', {})
        
        order_service = OrderService(session)
        await order_service.handle_payment_notification(event_type, resource)
        
        return Response(
            status_code=200,
            content='{"code": "SUCCESS", "message": "成功"}',
            media_type="application/json"
        )
        
    except Exception as e:
        logger.exception(f"Error processing WeChat Pay notification: {e}")
        return Response(status_code=500, content="Internal server error")


@order_router.get("/status/{order_id}")
async def get_order_status(
    order_id: int, 
    session: DBSession, 
    current_user: CurrentUser
):
    query = select(Order).where(
        Order.id == order_id,
        Order.user_id == current_user.id
    )
    result = await session.exec(query)
    order = result.first()
    
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    return {
        "id": order.id,
        "status": order.status,
        "amount": order.amount,
        "created_at": order.created_at,
        "updated_at": order.updated_at
    }


@order_router.get("/nurse/assigned", response_model=OrderList)
async def get_nurse_assigned_orders(
    session: DBSession,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 20,
    status: Optional[OrderStatus] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None
):
    """Get orders assigned to the current nurse"""
    from src.nurses.service import NurseService

    # Get nurse profile for current user
    nurse_service = NurseService(session)
    nurse = await nurse_service.get_nurse_by_user_id(current_user.id)

    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    # Query orders where current nurse is either primary or secondary nurse
    query = select(Order).options(
        selectinload(Order.service),
        selectinload(Order.patient),
        selectinload(Order.primary_nurse),
        selectinload(Order.secondary_nurse)
    ).where(
        or_(
            Order.primary_nurse_id == nurse.id,
            Order.secondary_nurse_id == nurse.id
        )
    )

    # Apply filters
    if status:
        query = query.where(Order.status == status)

    if date_from:
        query = query.where(Order.service_date >= date_from)

    if date_to:
        query = query.where(Order.service_date <= date_to)

    # Add ordering by service date and time
    query = query.order_by(Order.service_date.desc(), Order.service_time.desc())

    # Get total count
    count_query = select(func.count()).select_from(
        query.subquery()
    )
    total_result = await session.exec(count_query)
    total = total_result.one()

    # Apply pagination
    query = query.offset(skip).limit(limit)

    result = await session.exec(query)
    orders = result.all()

    return OrderList(orders=orders, total=total)


@order_router.get("/nurse/stats", response_model=dict)
async def get_nurse_order_stats(
    session: DBSession,
    current_user: CurrentUser
):
    """Get order statistics for the current nurse"""
    from src.nurses.service import NurseService

    # Get nurse profile for current user
    nurse_service = NurseService(session)
    nurse = await nurse_service.get_nurse_by_user_id(current_user.id)

    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    # Base query for nurse's orders
    base_query = select(Order).where(
        or_(
            Order.primary_nurse_id == nurse.id,
            Order.secondary_nurse_id == nurse.id
        )
    )

    # Total assigned orders
    total_orders_result = await session.exec(
        select(func.count()).select_from(base_query.subquery())
    )
    total_orders = total_orders_result.one()

    # Completed orders
    completed_orders_result = await session.exec(
        select(func.count()).select_from(
            base_query.where(Order.status == OrderStatus.COMPLETED).subquery()
        )
    )
    completed_orders = completed_orders_result.one()

    # Today's orders
    today = date.today()
    today_orders_result = await session.exec(
        select(func.count()).select_from(
            base_query.where(Order.service_date == today).subquery()
        )
    )
    today_orders = today_orders_result.one()

    # Pending orders (PAID status)
    pending_orders_result = await session.exec(
        select(func.count()).select_from(
            base_query.where(Order.status == OrderStatus.PAID).subquery()
        )
    )
    pending_orders = pending_orders_result.one()

    return {
        "total_orders": total_orders,
        "completed_orders": completed_orders,
        "today_orders": today_orders,
        "pending_orders": pending_orders,
        "completion_rate": round((completed_orders / total_orders * 100) if total_orders > 0 else 0, 1)
    }


@order_router.patch("/nurse/{order_id}/status", response_model=dict)
async def update_order_status_by_nurse(
    order_id: int,
    status_data: dict,
    session: DBSession,
    current_user: CurrentUser
):
    """Update order status by nurse (only for assigned orders)"""
    from src.nurses.service import NurseService

    # Get nurse profile for current user
    nurse_service = NurseService(session)
    nurse = await nurse_service.get_nurse_by_user_id(current_user.id)

    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    # Get the order and verify nurse assignment
    order = await session.get(Order, order_id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")

    # Check if current nurse is assigned to this order
    if order.primary_nurse_id != nurse.id and order.secondary_nurse_id != nurse.id:
        raise HTTPException(status_code=403, detail="You are not assigned to this order")

    # Validate status transition
    new_status = status_data.get('status')
    if not new_status:
        raise HTTPException(status_code=400, detail="Status is required")

    # Only allow certain status transitions for nurses
    allowed_transitions = {
        OrderStatus.PAID: [OrderStatus.PROCESSING],
        OrderStatus.PROCESSING: [OrderStatus.COMPLETED]
    }

    if order.status not in allowed_transitions or new_status not in allowed_transitions[order.status]:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot change status from {order.status} to {new_status}"
        )

    # Update order status
    order.status = OrderStatus(new_status)
    session.add(order)
    await session.commit()
    await session.refresh(order)

    return {
        "message": "Order status updated successfully",
        "order_id": order.id,
        "old_status": order.status,
        "new_status": new_status
    }


@order_router.patch("/{order_id}/start", response_model=OrderRead)
async def start_service(
    *,
    order_id: int,
    session: DBSession,
    current_user: CurrentUser
):
    """
    Start service for an order. Only nurses assigned to the order can start it.
    Changes order status from PAID to PROCESSING.
    """
    # Get the order with all relationships
    query = (
        select(Order)
        .where(Order.id == order_id)
        .options(
            selectinload(Order.service),
            selectinload(Order.patient),
            selectinload(Order.primary_nurse),
            selectinload(Order.secondary_nurse),
        )
    )
    result = await session.exec(query)
    order = result.first()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    # Check if current user is assigned as a nurse to this order
    # Compare with nurse's user_id, not nurse's id
    is_primary_nurse = order.primary_nurse and order.primary_nurse.user_id == current_user.id
    is_secondary_nurse = order.secondary_nurse and order.secondary_nurse.user_id == current_user.id

    if not (is_primary_nurse or is_secondary_nurse):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not assigned to this order"
        )

    # Check if order can be started
    if order.status != OrderStatus.PAID:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot start service for order with status: {order.status}"
        )

    # Update order status to PROCESSING
    order.status = OrderStatus.PROCESSING
    session.add(order)
    await session.commit()
    await session.refresh(order)

    return order


@order_router.patch("/{order_id}/complete", response_model=OrderRead)
async def complete_service(
    *,
    order_id: int,
    session: DBSession,
    current_user: CurrentUser
):
    """
    Complete service for an order. Only nurses assigned to the order can complete it.
    Changes order status from PROCESSING to COMPLETED.
    """
    # Get the order with all relationships
    query = (
        select(Order)
        .where(Order.id == order_id)
        .options(
            selectinload(Order.service),
            selectinload(Order.patient),
            selectinload(Order.primary_nurse),
            selectinload(Order.secondary_nurse),
        )
    )
    result = await session.exec(query)
    order = result.first()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    # Check if current user is assigned as a nurse to this order
    # Compare with nurse's user_id, not nurse's id
    is_primary_nurse = order.primary_nurse and order.primary_nurse.user_id == current_user.id
    is_secondary_nurse = order.secondary_nurse and order.secondary_nurse.user_id == current_user.id

    if not (is_primary_nurse or is_secondary_nurse):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not assigned to this order"
        )

    # Check if order can be completed
    if order.status != OrderStatus.PROCESSING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot complete service for order with status: {order.status}"
        )

    # Update order status to COMPLETED and set completion time
    order.status = OrderStatus.COMPLETED
    # order.completed_at = datetime.now(timezone.utc)
    session.add(order)
    await session.commit()
    await session.refresh(order)

    return order
