import asyncio
import json
import logging
import time
import uuid
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime
import random
import httpx

from src.utils.config import settings

logger = logging.getLogger(__name__)

class MockWeChatPay:
    """
    Mock WeChat Pay client that simulates payment functionality for development/testing.
    """
    
    def __init__(self, **kwargs):
        self.mchid = kwargs.get('mchid', 'MOCK_MERCHANT_ID')
        self.appid = kwargs.get('appid', 'MOCK_APP_ID')
        self.notify_url = settings.PAY_NOTIFY_URL or "http://localhost:8000/api/v1/orders/wechat-notify"
        
        # In-memory storage for mock transactions
        self._transactions = {}
        self._payment_delays = {}  # For simulating async payment processing
        
        logger.info("Mock Pay client initialized")

    
    async def pay(self, description: str, out_trade_no: str, amount: Dict, payer: Dict) -> <PERSON><PERSON>[int, Dict]:
        """
        Mock payment creation that simulates WeChat Pay JSAPI payment.
        Returns success response with mock payment parameters.
        """
        try:
            total_fee = amount.get('total', 0)
            openid = payer.get('openid', '')
            
            if not out_trade_no:
                return 400, {"code": "PARAM_ERROR", "message": "out_trade_no is required"}
            if total_fee <= 0:
                return 400, {"code": "PARAM_ERROR", "message": "Invalid amount"}
            if not openid:
                return 400, {"code": "PARAM_ERROR", "message": "openid is required"}
            
            # Check for duplicate order - update existing if found
            if out_trade_no in self._transactions:
                logger.info(f"Updating existing mock transaction for order {out_trade_no}")
                self._transactions[out_trade_no]['status'] = 'CREATED'
                self._transactions[out_trade_no]['created_at'] = datetime.now()
            else:
                transaction_id = f"MOCK_{int(time.time())}{random.randint(1000, 9999)}"
                self._transactions[out_trade_no] = {
                    'transaction_id': transaction_id,
                    'out_trade_no': out_trade_no,
                    'description': description,
                    'total_fee': total_fee,
                    'openid': openid,
                    'status': 'CREATED',
                    'created_at': datetime.now(),
                    'paid_at': None
                }
            
            # Generate mock payment parameters (same format as real WeChat Pay)
            timestamp = str(int(time.time()))
            nonce_str = uuid.uuid4().hex[:16]
            package = f"prepay_id=MOCK_PREPAY_{uuid.uuid4().hex[:16]}"
            
            payment_params = {
                'timeStamp': timestamp,
                'nonceStr': nonce_str,
                'package': package,
                'signType': 'RSA',
                'paySign': f"MOCK_SIGN_{uuid.uuid4().hex[:32]}"
            }
            
            delay = random.randint(5, 30)
            self._payment_delays[out_trade_no] = delay
            
            logger.info(f"Mock payment created for order {out_trade_no}, amount: ¥{total_fee/100:.2f}")
            logger.info(f"Simulated payment will auto-complete in {delay} seconds")
            
            # Schedule automatic payment completion (simulate user payment)
            asyncio.create_task(self._simulate_payment_completion(out_trade_no, delay))
            
            return 200, payment_params
            
        except Exception as e:
            logger.error(f"Mock payment creation error: {e}")
            return 500, {"code": "SYSTEM_ERROR", "message": str(e)}
    
    async def _simulate_payment_completion(self, out_trade_no: str, delay: int):
        """
        Simulate automatic payment completion after a delay.
        In real scenarios, this would be triggered by user action.
        """
        try:
            await asyncio.sleep(delay)
            
            if out_trade_no not in self._transactions:
                return
            
            transaction = self._transactions[out_trade_no]
            
            # Only auto-complete if still in CREATED status
            if transaction['status'] != 'CREATED':
                logger.info(f"Order {out_trade_no} status already changed to {transaction['status']}, skipping auto-completion")
                return
            
            success_rate = 0.8
            if random.random() < success_rate:
                transaction['status'] = 'SUCCESS'
                transaction['paid_at'] = datetime.now()
                
                await self._send_mock_notification(out_trade_no, 'TRANSACTION.SUCCESS')
                logger.info(f"Mock payment auto-completed successfully for order {out_trade_no}")
            else:
                transaction['status'] = 'PAYERROR'
                await self._send_mock_notification(out_trade_no, 'TRANSACTION.PAYERROR')
                logger.info(f"Mock payment auto-failed for order {out_trade_no}")
                
        except Exception as e:
            logger.error(f"Error in auto payment completion for {out_trade_no}: {e}")
    
    async def _send_mock_notification(self, out_trade_no: str, event_type: str):
        """
        Simulate sending webhook notification to your application.
        In development, make an HTTP request to your notify URL.
        """
        try:
            transaction = self._transactions.get(out_trade_no)
            if not transaction:
                logger.error(f"No transaction found for {out_trade_no} when sending notification")
                return
            
            # Create mock notification payload matching WeChat Pay format
            notification_data = {
                'event_type': event_type,
                'resource': {
                    'out_trade_no': out_trade_no,
                    'transaction_id': transaction['transaction_id'],
                    'trade_state': transaction['status'],
                    'amount': {'total': transaction['total_fee']},
                    'payer': {'openid': transaction['openid']},
                    'success_time': transaction['paid_at'].isoformat() if transaction['paid_at'] else None
                }
            }
            
            logger.info(f"Sending mock notification: {event_type} for {out_trade_no}")
            
            # Configure HTTP client with appropriate SSL settings
            timeout = httpx.Timeout(10.0)
            verify_ssl = False
            async with httpx.AsyncClient(timeout=timeout, verify=verify_ssl) as client:
                try:
                    response = await client.post(
                        self.notify_url,
                        json=notification_data,
                        headers={
                            'Content-Type': 'application/json',
                            'Wechatpay-Signature': f'MOCK_SIG_{uuid.uuid4().hex[:16]}',
                            'Wechatpay-Timestamp': str(int(time.time())),
                            'Wechatpay-Nonce': uuid.uuid4().hex[:16],
                            'Wechatpay-Serial': f'MOCK_SERI_{uuid.uuid4().hex[:8]}'
                        }
                    )
                    
                    if response.status_code == 200:
                        logger.info(f"Mock notification sent successfully for {out_trade_no}")
                    else:
                        logger.warning(f"Mock notification returned status {response.status_code} for {out_trade_no}: {response.text[:200]}")
                        
                except httpx.ConnectError as e:
                    logger.error(f"Connection error sending mock notification for {out_trade_no}: {e}")
                        
                except httpx.RequestError as e:
                    logger.error(f"Request error sending mock notification for {out_trade_no}: {e}")
                    
        except Exception as e:
            logger.error(f"Error sending mock notification for {out_trade_no}: {e}")
    

    def callback(self, headers: Dict, body: bytes) -> Dict:
        """
        Mock callback verification - always returns success for mock.
        """
        try:
            # Try to parse the JSON body
            try:
                notification_data = json.loads(body.decode('utf-8'))
                logger.info(f"Mock callback received: {notification_data.get('event_type')}")
                return notification_data
            except json.JSONDecodeError:
                # If not JSON, create a mock notification
                mock_notification = {
                    'event_type': 'TRANSACTION.SUCCESS',
                    'resource': {
                        'out_trade_no': 'mock_order_123',
                        'transaction_id': 'mock_txn_456',
                        'trade_state': 'SUCCESS',
                        'amount': {'total': 100},
                        'payer': {'openid': 'mock_openid'}
                    }
                }
                
                logger.info("Mock notification callback processed with fallback data")
                return mock_notification
            
        except Exception as e:
            logger.error(f"Mock callback error: {e}")
            raise e
    
    def get_transaction_status(self, out_trade_no: str) -> Optional[Dict]:
        """Get mock transaction status for testing"""
        return self._transactions.get(out_trade_no)
    
    def _manual_update_status(self, out_trade_no: str, new_status: str, event_type: str) -> bool:
        """Helper to manually update transaction status and send notification."""
        if out_trade_no in self._transactions:
            transaction = self._transactions[out_trade_no]
            if transaction['status'] in ['CREATED', 'PROCESSING']:
                transaction['status'] = new_status
                if new_status == 'SUCCESS':
                    transaction['paid_at'] = datetime.now()

                # Send notification asynchronously
                asyncio.create_task(self._send_mock_notification(out_trade_no, event_type))

                logger.info(f"Mock payment manually set to {new_status} for {out_trade_no}")
                return True
        return False

    def manual_complete_payment(self, out_trade_no: str) -> bool:
        """Manually complete a payment for testing purposes"""
        return self._manual_update_status(out_trade_no, 'SUCCESS', 'TRANSACTION.SUCCESS')
    
    def manual_fail_payment(self, out_trade_no: str) -> bool:
        """Manually fail a payment for testing purposes"""
        return self._manual_update_status(out_trade_no, 'PAYERROR', 'TRANSACTION.PAYERROR')
    
    def list_all_transactions(self) -> Dict:
        """List all mock transactions for debugging"""
        return self._transactions.copy()
    
    def force_notification_sync(self, out_trade_no: str) -> bool:
        """
        Force send notification synchronously for debugging
        """
        if out_trade_no not in self._transactions:
            return False
        
        transaction = self._transactions[out_trade_no]
        event_type = 'TRANSACTION.SUCCESS' if transaction['status'] == 'SUCCESS' else 'TRANSACTION.PAYERROR'
        
        # Run async notification in sync context for debugging
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        loop.run_until_complete(self._send_mock_notification(out_trade_no, event_type))
        return True


# Global mock client instance
mock_payment_client: Optional[MockWeChatPay] = None


def initialize_mock_client():
    """Initialize mock WeChat Pay client"""
    global mock_payment_client
    
    try:
        mock_payment_client = MockWeChatPay(
            mchid="MOCK_MERCHANT_ID",
            appid="MOCK_APP_ID"
        )
        return mock_payment_client
    except Exception as e:
        logger.error(f"Failed to initialize mock WeChat Pay client: {e}")
        return None


# Initialize mock client on import
mock_payment_client = initialize_mock_client()


async def create_jsapi_payment(order_description: str, out_trade_no: str, total_fee: int, user_openid: str):
    """
    Mock version of create_jsapi_payment function.
    Returns the same interface as the real WeChat Pay implementation.
    """
    if not mock_payment_client:
        logger.error("Mock WeChat Pay client not initialized")
        return False, "Mock payment service unavailable"
    
    try:
        code, message = await mock_payment_client.pay(
            description=order_description,
            out_trade_no=out_trade_no,
            amount={'total': total_fee},
            payer={'openid': user_openid}
        )
        
        if code == 200:
            logger.info(f"Mock payment successful for {out_trade_no}")
            return True, message
        else:
            logger.error(f"Mock payment error for {out_trade_no}: {message}")
            return False, message.get('message', 'Mock payment failed')
            
    except Exception as e:
        logger.error(f"Mock payment exception for {out_trade_no}: {e}")
        return False, "Mock payment system error"


async def parse_payment_notification(request_data: bytes, signature: str, timestamp: str, nonce: str, serial: str):
    """
    Mock version of parse_payment_notification.
    Always returns success with mock notification data.
    """
    if not mock_payment_client:
        return False, "Mock payment service not available"
    

    try:
        notification_data = json.loads(request_data.decode('utf-8'))
        logger.info(f"Mock notification parsed successfully: {notification_data.get('event_type')}")
    except json.JSONDecodeError:
        # Create mock notification if parsing fails
        notification_data = {
            'event_type': 'TRANSACTION.SUCCESS',
            'resource': {
                'out_trade_no': f'mock_order_{int(time.time())}',
                'transaction_id': f'mock_txn_{uuid.uuid4().hex[:16]}',
                'trade_state': 'SUCCESS'
            }
        }
        logger.info("Mock notification created with fallback data")
        
    return True, notification_data

# Development utilities
class MockPaymentTestUtils:
    """Utilities for testing mock payments"""
    
    @staticmethod
    def complete_payment(out_trade_no: str) -> bool:
        """Manually complete a payment for testing"""
        if mock_payment_client:
            return mock_payment_client.manual_complete_payment(out_trade_no)
        return False
    
    @staticmethod
    def fail_payment(out_trade_no: str) -> bool:
        """Manually fail a payment for testing"""
        if mock_payment_client:
            return mock_payment_client.manual_fail_payment(out_trade_no)
        return False
    
    @staticmethod
    def get_payment_status(out_trade_no: str) -> Optional[Dict]:
        """Get payment status for testing"""
        if mock_payment_client:
            return mock_payment_client.get_transaction_status(out_trade_no)
        return None
    
    @staticmethod
    def list_all_payments() -> Dict:
        """List all mock payments for debugging"""
        if mock_payment_client:
            return mock_payment_client.list_all_transactions()
        return {}
    
    @staticmethod
    def get_client_info() -> Dict:
        """Get mock client information"""
        if mock_payment_client:
            return {
                'initialized': True,
                'notify_url': mock_payment_client.notify_url,
                'mchid': mock_payment_client.mchid,
                'appid': mock_payment_client.appid,
                'total_transactions': len(mock_payment_client._transactions)
            }
        return {'initialized': False}
    
    @staticmethod
    def force_notification(out_trade_no: str) -> bool:
        """Force send notification for debugging"""
        if mock_payment_client:
            return mock_payment_client.force_notification_sync(out_trade_no)
        return False
    
    @staticmethod
    def test_connectivity() -> Dict:
        """Test notification endpoint connectivity"""
        if not mock_payment_client:
            return {'status': 'error', 'message': 'Mock client not initialized'}
        
        try:
            import requests
            url = mock_payment_client.notify_url
            
            results = {}
            try:
                response = requests.post(
                    url,
                    json={'test': 'connectivity'},
                    timeout=5,
                    verify=False
                )
                results = {
                    'status': 'success',
                    'status_code': response.status_code,
                    'accessible': True
                }
            except Exception as e:
                results = {
                    'status': 'error',
                    'error': str(e),
                    'accessible': False
                }
            
            return results
            
        except ImportError:
            return {'status': 'error', 'message': 'requests library not available'}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}


# Example usage in development/testing:
"""
# Test connectivity
connectivity = MockPaymentTestUtils.test_connectivity()
print(f"Connectivity test: {connectivity}")

# Get client info
info = MockPaymentTestUtils.get_client_info()
print(f"Client info: {info}")

# Force notification for debugging
success = MockPaymentTestUtils.force_notification("HC1234567890ABCDEF12")
print(f"Force notification: {success}")
"""
