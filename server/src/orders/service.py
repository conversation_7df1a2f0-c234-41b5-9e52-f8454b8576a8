import logging
import time
import uuid
from datetime import datetime, timedelta
from decimal import <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import HTTPEx<PERSON>, status
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import selectinload
from sqlmodel import select

from src.db.session import DBSession
from src.nurses.models import Nurse
from src.orders.models import Order, OrderCreate, OrderStatus, OrderUpdate
from src.orders.wechat_pay import create_jsapi_payment
from src.patients.models import Patient
from src.services.models import Service
from src.users.models import User

logger = logging.getLogger(__name__)


class OrderService:
    def __init__(self, session: DBSession):
        self.session = session

    async def _get_order_by_id_and_user(
        self, order_id: int, user_id: int, with_relations: bool = True
    ) -> Order:
        """Helper to get an order by ID and verify user ownership."""
        query = select(Order).where(Order.id == order_id, Order.user_id == user_id)
        if with_relations:
            query = query.options(
                selectinload(Order.service),
                selectinload(Order.patient),
                selectinload(Order.primary_nurse),
                selectinload(Order.secondary_nurse),
            )

        result = await self.session.exec(query)
        order = result.first()
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
            )
        return order

    async def _get_order_by_num(self, order_num: str) -> Optional[Order]:
        """Helper to get an order by its unique order number."""
        if not order_num:
            return None
        query = select(Order).where(Order.order_num == order_num)
        result = await self.session.exec(query)
        order = result.first()
        if not order:
            logger.warning(f"Order not found for order number: {order_num}")
        return order

    async def create_order(self, order_in: OrderCreate, current_user: User) -> dict:
        """Creates a new order and initiates payment."""
        if not current_user.openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User login session is invalid for payment.",
            )

        service = await self.session.get(Service, order_in.service_id)
        if not service or not service.is_active:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Service not found or inactive"
            )

        patient = await self.session.get(Patient, order_in.patient_id)
        if not patient or patient.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found or does not belong to user",
            )

        order = Order(
            order_num=f"HL{int(time.time())}{uuid.uuid4().hex[:8].upper()}",
            user_id=current_user.id,
            service_id=order_in.service_id,
            patient_id=order_in.patient_id,
            amount=Decimal(str(service.price)),
            status=OrderStatus.UNPAID,
            notes=order_in.notes,
            service_date=order_in.service_date,
            service_time=order_in.service_time,
        )

        try:
            self.session.add(order)
            await self.session.commit()
            await self.session.refresh(order, attribute_names=["service", "patient"])
        except IntegrityError as e:
            await self.session.rollback()
            logger.error(f"Database integrity error creating order: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create order due to database constraint",
            )

        try:
            success, payment_data = await create_jsapi_payment(
                order_description=f"预约服务: {service.name}",
                out_trade_no=order.order_num,
                total_fee=int(float(service.price) * 100),
                user_openid=current_user.openid,
            )

            if success:
                return {
                    "order_id": order.id,
                    "order_num": order.order_num,
                    "amount": float(order.amount),
                    "payment_params": payment_data,
                }
            else:
                order.status = OrderStatus.FAILED
                self.session.add(order)
                await self.session.commit()
                logger.error(
                    f"Failed to create prepay order for {order.order_num}. Reason: {payment_data}"
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Payment error: {payment_data}",
                )
        except Exception as e:
            order.status = OrderStatus.FAILED
            self.session.add(order)
            await self.session.commit()
            logger.exception(f"Error creating payment for order {order.order_num}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to initialize payment",
            )

    async def retry_payment(self, order_id: int, current_user: User) -> dict:
        """Retries payment for an existing unpaid or failed order."""
        if not current_user.openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User login session is invalid for payment.",
            )

        order = await self._get_order_by_id_and_user(order_id, current_user.id)

        if order.status not in [OrderStatus.UNPAID, OrderStatus.FAILED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot retry payment for order with status: {order.status}",
            )

        order.status = OrderStatus.UNPAID
        self.session.add(order)
        await self.session.commit()

        try:
            success, payment_data = await create_jsapi_payment(
                order_description=f"预约服务: {order.service.name}",
                out_trade_no=order.order_num,
                total_fee=int(float(order.amount) * 100),
                user_openid=current_user.openid,
            )

            if success:
                return {
                    "order_id": order.id,
                    "order_num": order.order_num,
                    "amount": float(order.amount),
                    "payment_params": payment_data,
                }
            else:
                order.status = OrderStatus.FAILED
                self.session.add(order)
                await self.session.commit()
                logger.error(
                    f"Failed to create retry payment for {order.order_num}. Reason: {payment_data}"
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Payment error: {payment_data}",
                )
        except Exception as e:
            order.status = OrderStatus.FAILED
            self.session.add(order)
            await self.session.commit()
            logger.exception(
                f"Error creating retry payment for order {order.order_num}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to initialize payment",
            )

    async def assign_nurses_to_order(self, order_id: int, assignment_data: OrderUpdate) -> Order:
        """
        Assigns or un-assigns nurses to a specific order.
        Validates that nurses exist and are active.
        Ensures primary and secondary nurses are not the same.
        """
        order = await self.session.get(Order, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
            )

        update_data = assignment_data.dict(exclude_unset=True)
        
        primary_nurse_id = update_data.get("primary_nurse_id", order.primary_nurse_id)
        secondary_nurse_id = update_data.get("secondary_nurse_id", order.secondary_nurse_id)

        if primary_nurse_id is not None and primary_nurse_id == secondary_nurse_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Primary and secondary nurses cannot be the same.",
            )

        if "primary_nurse_id" in update_data:
            nurse_id = update_data["primary_nurse_id"]
            if nurse_id is not None:
                nurse = await self.session.get(Nurse, nurse_id)
                if not nurse or not nurse.is_active:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Primary nurse with ID {nurse_id} not found or is inactive.",
                    )
            order.primary_nurse_id = nurse_id

        if "secondary_nurse_id" in update_data:
            nurse_id = update_data["secondary_nurse_id"]
            if nurse_id is not None:
                nurse = await self.session.get(Nurse, nurse_id)
                if not nurse or not nurse.is_active:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Secondary nurse with ID {nurse_id} not found or is inactive.",
                    )
            order.secondary_nurse_id = nurse_id
        
        self.session.add(order)
        await self.session.commit()
        # After the commit, the `order` object in the session is stale, especially
        # fields with server-side defaults like `updated_at`.
        # To prevent lazy-loading errors during response serialization, we re-fetch
        # the order with all required relationships eagerly loaded.
        query = (
            select(Order)
            .where(Order.id == order_id)
            .options(
                selectinload(Order.service),
                selectinload(Order.patient),
                selectinload(Order.primary_nurse),
                selectinload(Order.secondary_nurse),
            )
        )
        result = await self.session.exec(query)
        order = result.one()
        
        return order

    async def update_order_status_by_user(
        self, order_id: int, order_update: OrderUpdate, current_user: User
    ) -> Order:
        """Updates an order's status based on user actions (cancel, request refund, complete)."""
        
        # Admin users can update any order, regular users only their own
        if current_user.is_admin:
            query = (
                select(Order)
                .where(Order.id == order_id)
                .options(
                    selectinload(Order.service),
                    selectinload(Order.patient),
                    selectinload(Order.primary_nurse),
                    selectinload(Order.secondary_nurse),
                )
            )
            result = await self.session.exec(query)
            order = result.first()
            if not order:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
                )
        else:
            order = await self._get_order_by_id_and_user(order_id, current_user.id)

        if not order_update.status:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No status provided for update.",
            )

        new_status = order_update.status
        current_status = order.status

        if new_status == OrderStatus.CANCELED:
            allowed_statuses = [
                OrderStatus.UNPAID,
                OrderStatus.FAILED,
                OrderStatus.EXPIRED,
            ]
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot cancel order with status: {current_status}",
                )
            order.status = new_status
        elif new_status == OrderStatus.COMPLETED:
            # Allow completing orders that are paid or processing
            allowed_statuses = [OrderStatus.PAID, OrderStatus.PROCESSING]
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot complete order with status: {current_status}",
                )
            order.status = new_status
        elif new_status == OrderStatus.REFUNDING:
            if current_status != OrderStatus.PAID:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot request refund for order with status: {current_status}",
                )

            # Skip time validation for admin users
            if not current_user.is_admin:
                if not (order.service_date and order.service_time):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Service date and time are missing, cannot request refund.",
                    )

                try:
                    service_datetime = datetime.combine(order.service_date, order.service_time)
                    if service_datetime <= datetime.now() + timedelta(hours=24):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Refund can only be requested at least 24 hours before the service time.",
                        )
                except TypeError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid service date or time format.",
                    )
            order.status = new_status
        elif new_status == OrderStatus.REFUNDED:
            # Only admin can mark as refunded
            if not current_user.is_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only administrators can mark orders as refunded.",
                )
            if current_status != OrderStatus.REFUNDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot mark order as refunded with status: {current_status}",
                )
            order.status = new_status
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Status update to '{new_status}' is not supported by users.",
            )

        self.session.add(order)
        await self.session.commit()
        # Re-fetch the order with relationships to ensure the returned object is
        # fully loaded and not stale, preventing serialization issues.
        query = (
            select(Order)
            .where(Order.id == order_id)
            .options(
                selectinload(Order.service),
                selectinload(Order.patient),
                selectinload(Order.primary_nurse),
                selectinload(Order.secondary_nurse),
            )
        )
        result = await self.session.exec(query)
        refreshed_order = result.one()
        return refreshed_order

    async def delete_order(self, order_id: int, current_user: User):
        """Soft deletes an order by changing its status to DELETED."""
        order = await self._get_order_by_id_and_user(
            order_id, current_user.id, with_relations=False
        )

        allowed_statuses_for_delete = [
            OrderStatus.COMPLETED,
            OrderStatus.CANCELED,
            OrderStatus.REFUNDED,
        ]
        if order.status not in allowed_statuses_for_delete:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete order with status: {order.status}",
            )

        order.status = OrderStatus.DELETED
        self.session.add(order)
        await self.session.commit()

    async def handle_payment_notification(self, event_type: str, resource: dict):
        """Processes incoming payment notifications from WeChat Pay."""
        if event_type == "TRANSACTION.SUCCESS":
            await self._handle_payment_success(resource)
        elif event_type in ["TRANSACTION.CLOSED", "TRANSACTION.PAYERROR"]:
            await self._handle_payment_closed(resource)
        elif event_type == "REFUND.SUCCESS":
            await self._handle_refund_success(resource)
        else:
            logger.info(f"Received unhandled WeChat Pay event: {event_type}")

    async def _handle_payment_success(self, resource: dict):
        """Handle successful payment notification."""
        out_trade_no = resource.get("out_trade_no")
        order = await self._get_order_by_num(out_trade_no)
        if not order:
            return

        if order.status == OrderStatus.PAID:
            logger.warning(f"Order {out_trade_no} is already paid. Ignoring notification.")
            return

        if resource.get("trade_state") == "SUCCESS":
            order.status = OrderStatus.PAID
            order.transaction_id = resource.get("transaction_id")
            self.session.add(order)
            await self.session.commit()
            logger.info(f"Order {out_trade_no} marked as paid")

    async def _handle_payment_closed(self, resource: dict):
        """Handle payment closed/expired notification."""
        out_trade_no = resource.get("out_trade_no")
        order = await self._get_order_by_num(out_trade_no)
        if not order:
            return

        if order.status == OrderStatus.UNPAID:
            trade_state = resource.get("trade_state")
            if trade_state == "CLOSED":
                order.status = OrderStatus.EXPIRED
            elif trade_state == "PAYERROR":
                order.status = OrderStatus.FAILED

            self.session.add(order)
            await self.session.commit()
            logger.info(f"Order {out_trade_no} marked as {order.status}")

    async def _handle_refund_success(self, resource: dict):
        """Handle refund success notification."""
        out_trade_no = resource.get("out_trade_no")
        order = await self._get_order_by_num(out_trade_no)
        if not order:
            return

        if resource.get("refund_status") == "SUCCESS":
            order.status = OrderStatus.REFUNDED
            self.session.add(order)
            await self.session.commit()
            logger.info(f"Order {out_trade_no} refund completed")
