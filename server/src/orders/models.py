import enum
from datetime import datetime, date, time
from typing import Optional, List
from decimal import Decimal

from sqlmodel import Column, DateTime, SQLModel, Field, Relationship, func, Enum

from src.users.models import User
from src.services.models import Service, ServiceItem
from src.patients.models import Patient, PatientRead
from src.nurses.models import Nurse, NurseRead


class OrderStatus(enum.StrEnum):
    UNPAID = "UNPAID"
    PAID = "PAID"
    FAILED = "FAILED"
    CANCELED = "CANCELED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    REFUNDING = "REFUNDING"
    REFUNDED = "REFUNDED"
    EXPIRED = "EXPIRED"
    CLOSED = "CLOSED"
    DELETED = "DELETED"


class Order(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    order_num: str = Field(unique=True, index=True, max_length=64)
    transaction_id: Optional[str] = Field(default=None, unique=True, index=True, max_length=64)
    user_id: int = Field(foreign_key="user.id")
    service_id: int = Field(foreign_key="service.id")
    patient_id: int = Field(foreign_key="patient.id")

    service_date: Optional[date] = Field(default=None)
    service_time: Optional[time] = Field(default=None)

    # Nurse assignments - support for 2 nurses
    primary_nurse_id: Optional[int] = Field(default=None, foreign_key="nurse.id")
    secondary_nurse_id: Optional[int] = Field(default=None, foreign_key="nurse.id")

    # Use Decimal for precise currency handling instead of float
    amount: Decimal = Field(decimal_places=2, max_digits=10)
    status: OrderStatus = Field(
        default=OrderStatus.UNPAID,
        sa_column=Column(Enum(OrderStatus, native_enum=False), nullable=False)
    )

    # Add payment_method field for tracking
    payment_method: Optional[str] = Field(default="wechat_pay", max_length=32)
    
    # Add notes field for additional information
    notes: Optional[str] = Field(default=None, max_length=500)

    created_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())
    )

    user: User = Relationship(back_populates="orders")
    service: Service = Relationship()
    patient: Patient = Relationship()

    primary_nurse: Optional[Nurse] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Order.primary_nurse_id]"}
    )
    secondary_nurse: Optional[Nurse] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Order.secondary_nurse_id]"}
    )


class OrderCreate(SQLModel):
    service_id: int = Field(gt=0)  # Ensure positive ID
    patient_id: int = Field(gt=0)  # Ensure positive ID
    primary_nurse_id: Optional[int] = Field(default=None, gt=0)
    secondary_nurse_id: Optional[int] = Field(default=None, gt=0)
    notes: Optional[str] = Field(default=None, max_length=500)
    service_date: date
    service_time: time


class OrderRead(SQLModel):
    id: int
    order_num: str
    amount: Decimal
    status: OrderStatus
    service_date: Optional[date]
    service_time: Optional[time]
    payment_method: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    service: ServiceItem
    patient: PatientRead
    primary_nurse: Optional[NurseRead]
    secondary_nurse: Optional[NurseRead]
    
    class Config:
        # Handle Decimal serialization for JSON
        json_encoders = {
            Decimal: lambda v: float(v),
            time: lambda v: v.strftime('%H:%M') if v else None
        }


class OrderList(SQLModel):
    orders: List[OrderRead]
    total: int


class OrderUpdate(SQLModel):
    """For updating order status internally"""
    status: Optional[OrderStatus] = None
    transaction_id: Optional[str] = None
    notes: Optional[str] = None
    primary_nurse_id: Optional[int] = None
    secondary_nurse_id: Optional[int] = None



class OrderStatusHistory(SQLModel, table=True):
    """Optional: Track order status changes for audit purposes"""
    
    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="order.id")
    old_status: Optional[OrderStatus]
    new_status: OrderStatus
    changed_by: Optional[str] = Field(max_length=100)  # system, user, admin
    reason: Optional[str] = Field(max_length=200)

    created_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    )
    
    order: Order = Relationship()
