from fastapi import APIRouter, Depends, HTTPException, Query, status, File, UploadFile
from fastapi.responses import JSONResponse
import os
import uuid
from pathlib import Path
from src.db.session import DBSession
from src.services import crud, models
from typing import Optional, List
from sqlmodel import select
from sqlalchemy.orm import selectinload

# Helper function to map Service DB model to ServiceItem API model
def _map_service_to_item(service: models.Service) -> models.ServiceItem:
    service_item = models.ServiceItem.model_validate(service)
    if service.service_tag:
        service_item.tag = service.service_tag.name
    
    return service_item

# This router will handle general home-page and nursing-related endpoints
service_router = APIRouter(
    prefix="/services",
    tags=["services"],
    responses={404: {"description": "Not found"}},
)

# This router will handle service tag related endpoints
service_tag_router = APIRouter(
    prefix="/service-tags",
    tags=["service-tags"],
    responses={404: {"description": "Not found"}},
)

@service_router.get("/home", response_model=models.HomePageData)
async def get_home_page_data(db: DBSession):
    """
    This endpoint provides all the necessary data for the home page
    by fetching it from the PostgreSQL database using SQLModel.
    """
    # Fetch data from the database using CRUD functions
    db_banners = await crud.get_active_banners(db)
    db_categories = await crud.get_active_categories(db)
    db_services = await crud.get_recommended_services(db)

    # Map database models (snake_case) to API models (camelCase)
    banners = [models.BannerItem.model_validate(b) for b in db_banners]
    categories = [models.CategoryItem.model_validate(c) for c in db_categories]
    services = [_map_service_to_item(s) for s in db_services]

    return models.HomePageData(
        banners=banners,
        categories=categories,
        recommended_services=services,
    )


@service_router.get("/", response_model=models.ServicePaginated)
async def read_services(
    db: DBSession,
    skip: int = 0,
    limit: int = 20,
    search: Optional[str] = Query(None, alias="searchQuery"),
    tag: Optional[str] = Query(None, alias="tagFilter"),
    status: Optional[str] = Query(None, alias="statusFilter"),
    sort: str = Query("created_at_desc", alias="sortOrder"),
):
    """
    Retrieve a list of services with pagination, filtering, and sorting.
    """
    db_services, total = await crud.get_services(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        tag=tag,
        status=status,
        sort=sort,
    )

    services = [_map_service_to_item(s) for s in db_services]

    return models.ServicePaginated(total=total, services=services)


@service_router.get("/{service_id}", response_model=models.ServiceItem)
async def get_service_details(service_id: int, db: DBSession):
    """
    Get details for a specific service.
    """
    statement = (
        select(models.Service)
        .where(models.Service.id == service_id)
        .options(selectinload(models.Service.service_tag))
    )
    result = await db.exec(statement)
    service = result.first()

    if not service or not service.is_active:
        raise HTTPException(status_code=404, detail="Service not found")

    return _map_service_to_item(service)


@service_router.post("/", response_model=models.ServiceItem, status_code=status.HTTP_201_CREATED)
async def create_service(
    service_in: models.ServiceCreate,
    db: DBSession,
):
    """
    Create a new service.
    """
    # Check if service with the same name already exists
    existing_service_result = await db.exec(select(models.Service).where(models.Service.name == service_in.name))
    if existing_service_result.first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Service with this name already exists",
        )
    
    created_service = await crud.create_service(db=db, service_in=service_in)
    if not created_service:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Service tag '{service_in.tag}' not found",
        )
    
    return _map_service_to_item(created_service)


@service_router.patch("/{service_id}", response_model=models.ServiceItem)
async def update_service(
    service_id: int,
    service_update: models.ServiceUpdate,
    db: DBSession,
):
    """
    Update a service, e.g., to change its active status or other details.
    """
    statement = (
        select(models.Service)
        .where(models.Service.id == service_id)
    )
    result = await db.exec(statement)
    service = result.first()

    if not service:
        raise HTTPException(status_code=404, detail="Service not found")

    update_data = service_update.model_dump(exclude_unset=True)

    # Handle tag update separately if provided
    if "tag" in update_data and update_data["tag"] is not None:
        tag_name = update_data.pop("tag")
        tag_statement = select(models.ServiceTag).where(models.ServiceTag.name == tag_name)
        tag_result = await db.exec(tag_statement)
        service_tag = tag_result.first()
        if not service_tag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Service tag '{tag_name}' not found",
            )
        service.service_tag_id = service_tag.id

    for key, value in update_data.items():
        setattr(service, key, value)

    db.add(service)
    await db.commit()
    await db.refresh(service) # Refresh scalar attributes

    # Re-fetch with the relationship loaded to ensure the response is correct
    refreshed_statement = (
        select(models.Service)
        .where(models.Service.id == service_id)
        .options(selectinload(models.Service.service_tag))
    )
    refreshed_result = await db.exec(refreshed_statement)
    refreshed_service = refreshed_result.first()

    return _map_service_to_item(refreshed_service)


# --- Service Tag Endpoints ---

@service_tag_router.get("/active", response_model=List[str])
async def get_active_service_tags_api(db: DBSession):
    """
    Retrieve a list of all active service tag names for UI filters.
    """
    db_tags = await crud.get_active_service_tags(db)
    return [tag.name for tag in db_tags]

@service_tag_router.get("/all", response_model=List[models.ServiceTagItem])
async def get_all_service_tags_api(db: DBSession, skip: int = 0, limit: int = 100):
    """Retrieve all service tags for administrative purposes."""
    db_tags = await crud.get_all_service_tags(db, skip=skip, limit=limit)
    return db_tags

@service_tag_router.post("/", response_model=models.ServiceTagItem, status_code=status.HTTP_201_CREATED)
async def create_service_tag(tag: models.ServiceTagCreate, db: DBSession):
    """Create a new service tag."""
    existing_tag_result = await db.exec(select(models.ServiceTag).where(models.ServiceTag.name == tag.name))
    if existing_tag_result.first():
        raise HTTPException(status_code=400, detail="Tag with this name already exists")

    db_tag = models.ServiceTag.model_validate(tag)
    db.add(db_tag)
    await db.commit()
    await db.refresh(db_tag)
    return db_tag

@service_router.post("/upload-image", response_model=dict)
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image file and return the URL.
    """
    # Check if file is an image
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )
    
    # Check file size (limit to 5MB)
    if file.size and file.size > 5 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size must be less than 5MB"
        )
    
    # Generate unique filename
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported file format. Use JPG, PNG, GIF, or WebP"
        )
    
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # Create upload directory if it doesn't exist
    upload_dir = Path("static/uploads/images")
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Save file
    file_path = upload_dir / unique_filename
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )
    
    # Return the URL
    image_url = f"/static/uploads/images/{unique_filename}"
    return {"url": image_url}

@service_router.delete("/cleanup-images")
async def cleanup_unused_images(db: DBSession):
    """Clean up unused uploaded images (Admin only)"""
    import os
    import glob
    from pathlib import Path
    
    # Get all image files in upload directory
    upload_dir = Path("static/uploads/images")
    if not upload_dir.exists():
        return {"deleted": 0, "message": "Upload directory not found"}
    
    image_files = set(f.name for f in upload_dir.glob("*") if f.is_file())
    
    # Get all image URLs used in services
    services = await db.exec(select(models.Service))
    used_images = set()
    
    for service in services.all():
        if service.image_url and service.image_url.startswith('/static/uploads/images/'):
            filename = service.image_url.split('/')[-1]
            used_images.add(filename)
        
        # Check description for embedded images
        if service.description:
            import re
            matches = re.findall(r'/static/uploads/images/([^"\'>\s]+)', service.description)
            used_images.update(matches)
    
    # Delete unused files
    unused_files = image_files - used_images
    deleted_count = 0
    
    for filename in unused_files:
        try:
            os.remove(upload_dir / filename)
            deleted_count += 1
        except OSError:
            pass
    
    return {"deleted": deleted_count, "unused_files": list(unused_files)}
