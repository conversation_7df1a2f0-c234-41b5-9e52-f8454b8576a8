from typing import List, Optional
from sqlmodel import SQLModel, Field, Column, func, DateTime, Relationship
from datetime import datetime


class ServiceTagBase(SQLModel):
    name: str = Field(index=True, unique=True)
    is_active: bool = Field(default=True)

class ServiceTag(ServiceTagBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    services: List["Service"] = Relationship(back_populates="service_tag")

class BannerBase(SQLModel):
    image_url: str
    link_url: Optional[str] = None
    is_active: bool = Field(default=True, index=True)

class Banner(BannerBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)


class CategoryBase(SQLModel):
    name: str = Field(index=True)
    icon_url: str
    order: int = 0
    is_active: bool = Field(default=True)

class Category(CategoryBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)


class ServiceBase(SQLModel):
    name: str = Field(index=True)
    description: str  # Simple text description
    content: Optional[str] = None  # Rich text content from editor
    price: float
    image_url: str
    is_recommended: bool = Field(default=False, index=True)
    is_active: bool = Field(default=True, index=True)
    created_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    )

class Service(ServiceBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    service_tag_id: Optional[int] = Field(default=None, foreign_key="servicetag.id", index=True)
    service_tag: Optional[ServiceTag] = Relationship(back_populates="services")

class ServiceUpdate(SQLModel):
    name: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None  # Add content field
    price: Optional[float] = None
    image_url: Optional[str] = None
    is_recommended: Optional[bool] = None
    is_active: Optional[bool] = None
    tag: Optional[str] = None


class ServiceTagItem(ServiceTagBase):
    id: int

class ServiceTagCreate(ServiceTagBase):
    pass

class ServiceCreate(SQLModel):
    name: str
    description: str
    content: Optional[str] = None  # Add content field
    price: float
    tag: str
    image_url: Optional[str] = Field(default="/static/service/default.png")
    is_active: bool = Field(default=True)
    is_recommended: bool = Field(default=False)


class BannerItem(BannerBase):
    pass

class CategoryItem(CategoryBase):
    id: int

class ServiceItem(ServiceBase):
    id: int
    tag: Optional[str] = None

class HomePageData(SQLModel):
    banners: List[BannerItem]
    categories: List[CategoryItem]
    recommended_services: List[ServiceItem]

class ServicePaginated(SQLModel):
    total: int
    services: List[ServiceItem]
