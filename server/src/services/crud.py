from sqlmodel import select, func
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlalchemy.orm import selectinload
from src.services import models
from typing import Optional, Tuple, List

async def get_active_banners(db: AsyncSession):
    result = await db.exec(
        select(models.Banner).where(models.Banner.is_active == True)
    )
    return result.all()

async def get_active_categories(db: AsyncSession):
    result = await db.exec(
        select(models.Category).where(models.Category.is_active == True).order_by(models.Category.order)
    )
    return result.all()

async def get_active_service_tags(db: AsyncSession) -> List[models.ServiceTag]:
    """
    Retrieve all active service tags.
    """
    result = await db.exec(
        select(models.ServiceTag).where(models.ServiceTag.is_active == True).order_by(models.ServiceTag.name)
    )
    return result.all()

async def get_recommended_services(db: AsyncSession):
    result = await db.exec(
        select(models.Service).options(selectinload(models.Service.service_tag))
        .where(models.Service.is_recommended == True, models.Service.is_active == True)
    )
    return result.all()

async def get_services(
    db: AsyncSession,
    skip: int,
    limit: int,
    search: Optional[str],
    tag: Optional[str],
    status: Optional[str],
    sort: str,
) -> Tuple[List[models.Service], int]:
    """
    Get a paginated, filtered, and sorted list of services.
    """
    statement = select(models.Service).options(selectinload(models.Service.service_tag))

    # Filtering
    if search:
        statement = statement.where(models.Service.name.ilike(f"%{search}%"))
    if tag:
        statement = statement.join(models.ServiceTag).where(models.ServiceTag.name == tag)
    if status:
        if status == "active":
            statement = statement.where(models.Service.is_active == True)
        elif status == "inactive":
            statement = statement.where(models.Service.is_active == False)

    # Get total count before pagination and sorting for ordering
    count_statement = select(func.count()).select_from(statement.subquery())
    total_result = await db.exec(count_statement)
    total = total_result.one()

    sort_map = {
        "id_asc": models.Service.id.asc(),
        "id_desc": models.Service.id.desc(),
        "name_asc": models.Service.name.asc(),
        "name_desc": models.Service.name.desc(),
        "created_at_asc": models.Service.created_at.asc(),
        "created_at_desc": models.Service.created_at.desc(),
    }
    if sort in sort_map:
        statement = statement.order_by(sort_map[sort])
    else:
        statement = statement.order_by(models.Service.created_at.desc())

    # Pagination
    statement = statement.offset(skip).limit(limit)

    # Execute query
    result = await db.exec(statement)
    services = result.all()

    return services, total

async def create_service(db: AsyncSession, service_in: models.ServiceCreate) -> Optional[models.Service]:
    """
    Create a new service. Returns the created service or None if tag not found.
    """
    # Find the service tag by name
    tag_result = await db.exec(select(models.ServiceTag).where(models.ServiceTag.name == service_in.tag))
    service_tag = tag_result.first()
    if not service_tag:
        return None

    # Create a dictionary of the service data, excluding the tag name
    service_data = service_in.model_dump(exclude={"tag"})
    
    # Create the service object with the tag id
    db_service = models.Service(**service_data, service_tag_id=service_tag.id)
    
    db.add(db_service)
    await db.commit()
    await db.refresh(db_service)
    # Eagerly load the service_tag relationship for the response
    await db.refresh(db_service, attribute_names=["service_tag"])
    return db_service

async def get_all_service_tags(db: AsyncSession, skip: int, limit: int) -> List[models.ServiceTag]:
    """
    Retrieve all service tags with pagination.
    """
    result = await db.exec(
        select(models.ServiceTag).order_by(models.ServiceTag.id).offset(skip).limit(limit)
    )
    return result.all()