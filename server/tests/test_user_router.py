import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlmodel import Session

from src.user.model import User
from src.auth.auth import get_passwd_hash


@pytest.mark.asyncio
async def test_create_user(client: TestClient):
    response = client.post(
        "/auth/register", json={"username": "testuser", "email": "<EMAIL>", "password": "password123", "phone": "************"}
    )
    assert response.status_code == 200
    assert response.json()["username"] == "testuser"
    assert response.json()["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_list_users(client: TestClient, session: Session):
    # Create a user directly in the session for testing list
    user_1 = User(username="user1", email="<EMAIL>", passwd_hash=get_passwd_hash("password1"), phone="************")
    user_2 = User(username="user2", email="<EMAIL>", passwd_hash=get_passwd_hash("password2"), phone="************")
    session.add(user_1)
    await session.commit() # Await commit
    await session.refresh(user_1) # Await refresh
    session.add(user_2)
    await session.commit() # Await commit
    await session.refresh(user_2) # Await refresh

    response = client.get("/users/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2
    assert data[0]["username"] == "user1"
    assert data[1]["username"] == "user2"


@pytest.mark.asyncio
async def test_get_user(client: TestClient, session: Session):
    user = User(username="singleuser", email="<EMAIL>", passwd_hash=get_passwd_hash("password"), phone="************")
    session.add(user)
    await session.commit() # Await commit
    await session.refresh(user) # Await refresh

    response = client.get(f"/users/{user.id}")
    assert response.status_code == 200
    assert response.json()["username"] == "singleuser"

    response = client.get("/users/999")  # Non-existent user
    assert response.status_code == 404
    assert response.json()["detail"] == "User not found"


@pytest.mark.asyncio
async def test_update_user(client: TestClient, session: Session):
    user = User(username="oldname", email="<EMAIL>", passwd_hash=get_passwd_hash("oldpassword"), phone="************")
    session.add(user)
    await session.commit() # Await commit
    await session.refresh(user) # Await refresh

    response = client.put(
        f"/users/{user.id}", json={"email": "<EMAIL>", "phone": "************"}
    )
    assert response.status_code == 200
    assert response.json()["email"] == "<EMAIL>"

    response = client.put(
        "/users/999", json={"phone": "************"}
    )
    assert response.status_code == 404
    assert response.json()["detail"] == "User not found"


@pytest.mark.asyncio
async def test_delete_user(client: TestClient, session: Session):
    user = User(username="todelete", email="<EMAIL>", passwd_hash=get_passwd_hash("password"), phone="************")
    session.add(user)
    await session.commit() # Await commit
    await session.refresh(user) # Await refresh

    response = client.delete(f"/users/{user.id}")
    assert response.status_code == 200
    assert response.json()["username"] == "todelete"

    response = client.get(f"/users/{user.id}")
    assert response.status_code == 404

    response = client.delete("/users/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "User not found"
