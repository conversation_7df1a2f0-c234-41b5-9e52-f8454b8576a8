"""add_license_documents_to_nurse

Revision ID: 8e395e488a38
Revises: 324cebb66893
Create Date: 2025-10-13 09:05:06.636376

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '8e395e488a38'
down_revision: Union[str, None] = '324cebb66893'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nurse', sa.Column('license_documents', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.alter_column('nurse', 'title',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('nurse', 'license_number',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('nurse', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('nurse', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('nurse', 'license_number',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('nurse', 'title',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.drop_column('nurse', 'license_documents')
    # ### end Alembic commands ###
