"""Create initial admin user

Revision ID: create_admin_user
Revises: f3ed4e63d073
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from src.auth.password import get_password_hash

# revision identifiers
revision = 'create_admin_user'
down_revision = '252a7aaa7dea'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create a table reference for the user table
    user_table = table('user',
        column('username', sa.String),
        column('hashed_password', sa.String),
        column('is_admin', sa.Bo<PERSON>an),
        column('is_active', sa.Bo<PERSON>),
        column('openid', sa.String),
        column('session_key', sa.String),
    )
    
    # Hash the default password
    hashed_password = get_password_hash("admin123")
    
    # Insert the admin user
    op.bulk_insert(user_table, [
        {
            'username': 'admin',
            'hashed_password': hashed_password,
            'is_admin': True,
            'is_active': True,
            'openid': None,
            'session_key': None,
        }
    ])

def downgrade() -> None:
    # Remove the admin user
    op.execute("DELETE FROM \"user\" WHERE username = 'admin'")