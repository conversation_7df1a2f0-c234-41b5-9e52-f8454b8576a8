"""init

Revision ID: 252a7aaa7dea
Revises: 
Create Date: 2025-09-01 11:19:57.413696

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '252a7aaa7dea'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('banner',
    sa.Column('image_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('link_url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_banner_is_active'), 'banner', ['is_active'], unique=False)
    op.create_table('category',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('icon_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_category_name'), 'category', ['name'], unique=False)
    op.create_table('servicetag',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_servicetag_name'), 'servicetag', ['name'], unique=True)
    op.create_table('user',
    sa.Column('openid', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('session_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('nickname', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('avatar', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('phone', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('username', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('hashed_password', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_admin', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_openid'), 'user', ['openid'], unique=True)
    op.create_index(op.f('ix_user_username'), 'user', ['username'], unique=True)
    op.create_table('nurse',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('gender', sa.Enum('MALE', 'FEMALE', name='nursegender'), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('department', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('license_number', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=True),
    sa.Column('years_of_experience', sa.Integer(), nullable=True),
    sa.Column('specialties', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_nurse_name'), 'nurse', ['name'], unique=False)
    op.create_table('patient',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('phone', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('id_num', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('address', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_name'), 'patient', ['name'], unique=False)
    op.create_table('service',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('image_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('is_recommended', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service_tag_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['service_tag_id'], ['servicetag.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_service_is_active'), 'service', ['is_active'], unique=False)
    op.create_index(op.f('ix_service_is_recommended'), 'service', ['is_recommended'], unique=False)
    op.create_index(op.f('ix_service_name'), 'service', ['name'], unique=False)
    op.create_index(op.f('ix_service_service_tag_id'), 'service', ['service_tag_id'], unique=False)
    op.create_table('order',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_num', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('transaction_id', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('service_id', sa.Integer(), nullable=False),
    sa.Column('patient_id', sa.Integer(), nullable=False),
    sa.Column('service_date', sa.Date(), nullable=True),
    sa.Column('service_time', sa.Time(), nullable=True),
    sa.Column('primary_nurse_id', sa.Integer(), nullable=True),
    sa.Column('secondary_nurse_id', sa.Integer(), nullable=True),
    sa.Column('amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('status', sa.Enum('UNPAID', 'PAID', 'FAILED', 'CANCELED', 'PROCESSING', 'COMPLETED', 'REFUNDING', 'REFUNDED', 'EXPIRED', 'CLOSED', 'DELETED', name='orderstatus', native_enum=False), nullable=False),
    sa.Column('payment_method', sqlmodel.sql.sqltypes.AutoString(length=32), nullable=True),
    sa.Column('notes', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['patient_id'], ['patient.id'], ),
    sa.ForeignKeyConstraint(['primary_nurse_id'], ['nurse.id'], ),
    sa.ForeignKeyConstraint(['secondary_nurse_id'], ['nurse.id'], ),
    sa.ForeignKeyConstraint(['service_id'], ['service.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_order_num'), 'order', ['order_num'], unique=True)
    op.create_index(op.f('ix_order_transaction_id'), 'order', ['transaction_id'], unique=True)
    op.create_table('orderstatushistory',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('old_status', sa.Enum('UNPAID', 'PAID', 'FAILED', 'CANCELED', 'PROCESSING', 'COMPLETED', 'REFUNDING', 'REFUNDED', 'EXPIRED', 'CLOSED', 'DELETED', name='orderstatus'), nullable=True),
    sa.Column('new_status', sa.Enum('UNPAID', 'PAID', 'FAILED', 'CANCELED', 'PROCESSING', 'COMPLETED', 'REFUNDING', 'REFUNDED', 'EXPIRED', 'CLOSED', 'DELETED', name='orderstatus'), nullable=False),
    sa.Column('changed_by', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('reason', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['order_id'], ['order.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('orderstatushistory')
    op.drop_index(op.f('ix_order_transaction_id'), table_name='order')
    op.drop_index(op.f('ix_order_order_num'), table_name='order')
    op.drop_table('order')
    op.drop_index(op.f('ix_service_service_tag_id'), table_name='service')
    op.drop_index(op.f('ix_service_name'), table_name='service')
    op.drop_index(op.f('ix_service_is_recommended'), table_name='service')
    op.drop_index(op.f('ix_service_is_active'), table_name='service')
    op.drop_table('service')
    op.drop_index(op.f('ix_patient_name'), table_name='patient')
    op.drop_table('patient')
    op.drop_index(op.f('ix_nurse_name'), table_name='nurse')
    op.drop_table('nurse')
    op.drop_index(op.f('ix_user_username'), table_name='user')
    op.drop_index(op.f('ix_user_openid'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_servicetag_name'), table_name='servicetag')
    op.drop_table('servicetag')
    op.drop_index(op.f('ix_category_name'), table_name='category')
    op.drop_table('category')
    op.drop_index(op.f('ix_banner_is_active'), table_name='banner')
    op.drop_table('banner')
    # ### end Alembic commands ###
