"""update_patient_address_fields

Revision ID: e27779a49d66
Revises: 8e395e488a38
Create Date: 2025-10-22 15:13:11.159036

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'e27779a49d66'
down_revision: Union[str, None] = '8e395e488a38'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new columns as nullable first
    op.add_column('patient', sa.Column('base_address', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('patient', sa.Column('detail_address', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('patient', sa.Column('latitude', sa.Float(), nullable=True))
    op.add_column('patient', sa.Column('longitude', sa.Float(), nullable=True))

    # Migrate existing data: copy address to base_address and set detail_address to empty string
    op.execute("UPDATE patient SET base_address = address, detail_address = '' WHERE address IS NOT NULL")

    # Make base_address and detail_address non-nullable after data migration
    op.alter_column('patient', 'base_address', nullable=False)
    op.alter_column('patient', 'detail_address', nullable=False)

    # Drop the old address column
    op.drop_column('patient', 'address')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add back the address column as nullable first
    op.add_column('patient', sa.Column('address', sa.VARCHAR(), autoincrement=False, nullable=True))

    # Migrate data back: combine base_address and detail_address into address
    op.execute("""
        UPDATE patient
        SET address = CASE
            WHEN detail_address IS NOT NULL AND detail_address != ''
            THEN base_address || ' ' || detail_address
            ELSE base_address
        END
        WHERE base_address IS NOT NULL
    """)

    # Make address non-nullable after data migration
    op.alter_column('patient', 'address', nullable=False)

    # Drop the new columns
    op.drop_column('patient', 'longitude')
    op.drop_column('patient', 'latitude')
    op.drop_column('patient', 'detail_address')
    op.drop_column('patient', 'base_address')
    # ### end Alembic commands ###
