services:
  api:
    image: homecare
    build: .
    container_name: homecare_api
    restart: always
    volumes:
      - ./src:/code/src
      - ./static:/code/static
      - ./certs:/code/certs
      - ./alembic:/code/alembic
      - ./alembic.ini:/code/alembic.ini
    ports:
      - 8000:8000
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy

  db:
    image: postgres:17-alpine
    container_name: homecare_db
    restart: always
    env_file:
      - .env
    volumes:
      - ./homecare_data:/var/lib/postgresql/data
    ports:
      - ${POSTGRES_PORT}:${POSTGRES_PORT}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5


