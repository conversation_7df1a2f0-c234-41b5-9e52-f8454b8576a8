{"name": "bemg", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@tiptap/extension-character-count": "^3.4.3", "@tiptap/extension-code-block-lowlight": "^3.4.3", "@tiptap/extension-color": "^3.4.4", "@tiptap/extension-image": "^3.4.3", "@tiptap/extension-link": "^3.4.3", "@tiptap/extension-placeholder": "^3.4.4", "@tiptap/extension-table": "^3.4.3", "@tiptap/extension-table-cell": "^3.4.3", "@tiptap/extension-table-header": "^3.4.3", "@tiptap/extension-table-row": "^3.4.3", "@tiptap/extension-text-style": "^3.4.4", "@tiptap/extension-underline": "^3.4.3", "@tiptap/starter-kit": "^3.4.3", "@tiptap/vue-3": "^3.4.3", "axios": "^1.11.0", "echarts": "^6.0.0", "element-plus": "^2.10.5", "lowlight": "^3.3.0", "pinia": "^3.0.3", "tiptap-extension-resize-image": "^1.3.1", "undefined": "^0.1.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}