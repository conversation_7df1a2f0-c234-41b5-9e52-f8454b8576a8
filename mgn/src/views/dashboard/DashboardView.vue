<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { List, User, Briefcase, Money } from '@element-plus/icons-vue';
import { getSummaryStats, getDailyOrderStats, getServiceTypeStats, getServiceTimeHeatmap } from '@/services/api';
import type { ServiceTimeHeatmapData } from '@/services/api';
import StatCard from '@/views/components/StatCard.vue';
import * as echarts from 'echarts';

const stats = ref({
  completed_orders: 0,
  processing_orders: 0,
  todays_orders: 0,
  total_revenue: 0,
});

const loading = ref(true);
let dailyOrdersChart: echarts.ECharts | null = null;
let serviceTypeChart: echarts.ECharts | null = null;
let serviceTimeHeatmapChart: echarts.ECharts | null = null;

const fetchStats = async () => {
  loading.value = true;
  try {
    stats.value = await getSummaryStats();
    await fetchDailyOrders();
    await fetchServiceTypeStats();
    await fetchServiceTimeHeatmap();
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
  } finally {
    loading.value = false;
  }
};

const fetchDailyOrders = async () => {
  try {
    const [totalOrders, completedOrders] = await Promise.all([
      getDailyOrderStats(30), // Total orders, uses created_at by default
      getDailyOrderStats(30, 'COMPLETED', 'service_date') // Completed orders, by service_date
    ]);
    renderDailyOrdersChart(totalOrders, completedOrders);
  } catch (error) {
    console.error('Error fetching daily orders:', error);
  }
};

const fetchServiceTypeStats = async () => {
  try {
    const response = await getServiceTypeStats();
    renderServiceTypeChart(response);
  } catch (error) {
    console.error('Error fetching service type stats:', error);
  }
};

const fetchServiceTimeHeatmap = async () => {
  try {
    const response = await getServiceTimeHeatmap();
    renderServiceTimeHeatmap(response);
  } catch (error) {
    console.error('Error fetching service time heatmap:', error);
  }
};

const renderDailyOrdersChart = (
  totalData: Array<{date: string, count: number}>,
  completedData: Array<{date: string, count: number}>
) => {
  const chartDom = document.getElementById('daily-orders-chart');
  if (!chartDom) return;

  dailyOrdersChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);

  const totalCounts = totalData.map(item => item.count);
  const completedCounts = completedData.map(item => item.count);
  const maxValue = Math.max(...totalCounts);

  const option = {
    title: {
      text: '近30天订单量',
      left: 'center',
      top: 10
    },
    legend: {
      data: ['新建订单', '已完成订单'],
      bottom: 20,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%', // Make space for legend
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: totalData.map(item => item.date.substring(5)), // Show only month-day
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '订单量',
      min: 0,
      max: maxValue > 0 ? Math.ceil(maxValue * 1.1) : 5,
      interval: Math.ceil((maxValue > 0 ? Math.ceil(maxValue * 1.1) : 5) / 5),
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '新建订单',
        type: 'bar',
        data: totalCounts,
        itemStyle: {
          color: '#409EFF'
        },
      },
      {
        name: '已完成订单',
        type: 'bar',
        data: completedCounts,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  };

  dailyOrdersChart.setOption(option);

  window.addEventListener('resize', function() {
    dailyOrdersChart?.resize();
  });
};

const renderServiceTypeChart = (data: Array<{name: string, value: number}>) => {
  const chartDom = document.getElementById('service-type-chart');
  if (!chartDom) return;

  serviceTypeChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);

  const option = {
    title: {
      text: '服务类型订单占比',
      left: 'center',
      top: 20
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      top: 'bottom',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '订单占比',
        type: 'pie',
        radius: [20, 100], // 内半径20，外半径100
        center: ['50%', '50%'],
        roseType: 'radius', // 南丁格尔玫环图
        itemStyle: {
          borderRadius: 5
        },
        label: {
          show: true,
          formatter: '{b}: {d}%'
        },
        emphasis: {
          label: {
            show: true
          }
        },
        data: data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getRandomColor() // 随机颜色或使用固定配色
          }
        }))
      }
    ]
  };

  serviceTypeChart.setOption(option);

  window.addEventListener('resize', function() {
    serviceTypeChart?.resize();
  });
};

const renderServiceTimeHeatmap = (data: ServiceTimeHeatmapData[]) => {
  const chartDom = document.getElementById('service-time-heatmap');
  if (!chartDom) return;

  serviceTimeHeatmapChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);

  // Business hours: Monday-Friday, 9:00 AM - 5:00 PM
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  const hours = Array.from({ length: 9 }, (_, i) => i + 9); // 9-17 (9 AM - 5 PM)

  // Create a map for quick lookup
  const dataMap = new Map<string, number>();
  data.forEach(item => {
    const key = `${item.day}-${item.hour}`;
    dataMap.set(key, item.count);
  });

  // Convert to heatmap format: [hourIndex, dayIndex, count]
  const heatmapData = [];
  const maxCount = Math.max(...data.map(item => item.count), 1);

  for (let dayIndex = 0; dayIndex < days.length; dayIndex++) {
    for (let hourIndex = 0; hourIndex < hours.length; hourIndex++) {
      const hour = hours[hourIndex];
      const key = `${days[dayIndex]}-${hour}`;
      const count = dataMap.get(key) || 0;
      heatmapData.push([hourIndex, dayIndex, count]);
    }
  }

  const option = {
    title: {
      text: '服务时间分布热力图',
      subtext: '工作日 9:00 - 17:00',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: function (params: { data: [number, number, number] }) {
        const hourIndex = params.data[0];
        const hour = hours[hourIndex];
        const dayName = days[params.data[1]];
        const count = params.data[2];
        const timeStr = `${hour.toString().padStart(2, '0')}:00`;
        return `${dayName} ${timeStr}<br/>预约数量: ${count}`;
      }
    },
    grid: {
      height: '60%',
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: hours.map(h => `${h.toString().padStart(2, '0')}:00`),
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 12
      },
      name: '时间 (小时)',
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      },
      name: '工作日',
      nameLocation: 'middle',
      nameGap: 60
    },
    visualMap: {
      min: 0,
      max: maxCount,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#f8f9fa', '#e3f2fd', '#bbdefb', '#90caf9', '#64b5f6', '#42a5f5', '#2196f3', '#1e88e5', '#1976d2', '#1565c0']
      },
      text: ['高', '低'],
      textStyle: {
        color: '#333'
      }
    },
    series: [{
      name: '服务预约',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  serviceTimeHeatmapChart.setOption(option);

  window.addEventListener('resize', function() {
    serviceTimeHeatmapChart?.resize();
  });
};

const getRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

onMounted(() => {
  fetchStats();
});

// Clean up charts when component is unmounted
onUnmounted(() => {
  if (dailyOrdersChart) {
    dailyOrdersChart.dispose();
    dailyOrdersChart = null;
  }
  if (serviceTypeChart) {
    serviceTypeChart.dispose();
    serviceTypeChart = null;
  }
  if (serviceTimeHeatmapChart) {
    serviceTimeHeatmapChart.dispose();
    serviceTimeHeatmapChart = null;
  }
});

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US').format(num || 0);
};

const formatCurrency = (num: number) => {
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(num || 0);
};
</script>

<template>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <StatCard
          title="已完成订单"
          :value="formatNumber(stats.completed_orders)"
          :icon="List"
          color="#409EFC"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="待完成订单"
          :value="formatNumber(stats.processing_orders)"
          :icon="User"
          color="#67C23A"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="今日订单"
          :value="formatNumber(stats.todays_orders)"
          :icon="Briefcase"
          color="#E6A23C"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="总收入"
          :value="formatCurrency(stats.total_revenue)"
          :icon="Money"
          color="#F56C6C"
          :loading="loading"
        />
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card class="box-card">
          <div id="daily-orders-chart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb-4">
      <el-col :span="12">
        <el-card class="box-card">
          <div id="service-type-chart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div id="service-time-heatmap" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

</template>

<style scoped>
.box-card {
  margin-top: 20px;
}
</style>
