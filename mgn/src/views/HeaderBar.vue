<script setup lang="ts">
import { Fold, Expand, User, <PERSON><PERSON>en, SwitchButton } from '@element-plus/icons-vue';
import { useLayoutStore } from '@/stores/layout';
import { storeToRefs } from 'pinia';

import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const layoutStore = useLayoutStore();
const { isCollapse, breadcrumbs } = storeToRefs(layoutStore);
const { toggleCollapse } = layoutStore;


// Placeholder functions for dropdown menu
const handleCommand = (command: string) => {
  switch (command) {
    case 'password':
      console.log('Change password clicked');
      // Add change password logic here
      break;
    case 'logout':
      authStore.logout()
      router.push('/login')
      break;
  }
};
</script>

<template>
  <el-header class="main-header">
    <div class="header-content">
      <div class="header-left">
        <el-button
          :icon="isCollapse ? Expand : Fold"
          @click="toggleCollapse"
          circle
          class="collapse-btn"
        />
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            <el-avatar :icon="User" size="small" />
            <span class="username">Admin</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :icon="EditPen" command="password">修改密码</el-dropdown-item>
              <el-dropdown-item :icon="SwitchButton" command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-header>
</template>

<style scoped>
.main-header {
  background-color: #B3C0D1;
  color: #333;
  height: 56px;
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 20px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.breadcrumb {
  margin-left: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 8px;
}
</style>
