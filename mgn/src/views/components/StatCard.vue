<script setup lang="ts">
import type { Component } from 'vue';

defineProps<{
  title: string;
  value: string | number;
  icon: Component;
  color: string;
  loading: boolean;
}>();
</script>

<template>
  <el-card shadow="hover" v-loading="loading">
    <div class="card-content">
      <el-icon class="card-icon" :size="40" :color="color">
        <component :is="icon" />
      </el-icon>
      <div class="card-text">
        <div class="card-num">{{ value }}</div>
        <div class="card-title">{{ title }}</div>
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.card-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.card-icon {
  margin-right: 15px;
}

.card-text {
  display: flex;
  flex-direction: column;
}

.card-num {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-title {
  font-size: 14px;
  color: #909399;
}
</style>

