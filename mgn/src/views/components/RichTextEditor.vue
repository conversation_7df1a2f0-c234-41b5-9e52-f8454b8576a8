<template>
  <div class="rich-editor">
    <div class="editor-toolbar" v-if="editor">

      <!-- Heading dropdown -->
      <div class="toolbar-group">
        <div class="dropdown" ref="headingDropdown">
          <button
            @click="toggleHeadingDropdown"
            class="dropdown-toggle"
            :class="{ 'is-active': isHeadingActive }"
            title="Text Format"
          >
            <span class="dropdown-text">{{ currentHeadingText }}</span>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" class="dropdown-arrow">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>
          <div class="dropdown-menu" v-show="showHeadingDropdown">
            <button
              @click="setHeading('paragraph')"
              :class="{ 'is-active': editor.isActive('paragraph') }"
              class="dropdown-item"
            >
              正文
            </button>
            <button
              @click="setHeading(1)"
              :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
              class="dropdown-item heading-1"
            >
              标题1
            </button>
            <button
              @click="setHeading(2)"
              :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
              class="dropdown-item heading-2"
            >
              标题2
            </button>
            <button
              @click="setHeading(3)"
              :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
              class="dropdown-item heading-3"
            >
              标题3
            </button>
          </div>
        </div>
      </div>

      <div class="toolbar-divider"></div>

      <!-- Text formatting group -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
          title="Bold (Ctrl+B)"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z"/>
          </svg>
        </button>
        <button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
          title="Italic (Ctrl+I)"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z"/>
          </svg>
        </button>
        <button
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
          title="Underline (Ctrl+U)"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
          </svg>
        </button>
        <button
          @click="editor.chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
          title="Strikethrough"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 19h4v-3h-4v3zM5 4v3h5v3h4V7h5V4H5zM3 14h18v-2H3v2z"/>
          </svg>
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- Text Color dropdown -->
      <div class="toolbar-group">
        <div class="dropdown" ref="colorDropdown">
          <button
            @click="toggleColorDropdown"
            class="dropdown-toggle color-toggle"
            title="Text Color"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9.93 13.5h4.14L12 7.98 9.93 13.5zM12 2l7.5 18h-3.12L15.05 17H8.95L7.62 20H4.5L12 2z"/>
            </svg>
            <div class="color-indicator" :style="{ backgroundColor: currentTextColor }"></div>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" class="dropdown-arrow">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>
          <div class="dropdown-menu color-menu" v-show="showColorDropdown">
            <div class="color-grid">
              <button
                v-for="color in colorPalette"
                :key="color"
                @click="setTextColor(color)"
                class="color-swatch"
                :style="{ backgroundColor: color }"
                :title="color"
              ></button>
            </div>
            <div class="color-actions">
              <button @click="removeTextColor" class="color-action">
                Remove Color
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="toolbar-divider"></div>

      <!-- List group -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor.isActive('bulletList') }"
          title="Bullet List"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z"/>
          </svg>
        </button>
        <button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor.isActive('orderedList') }"
          title="Numbered List"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>
          </svg>
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- Media group -->
      <div class="toolbar-group">
        <button @click="addImage" title="Add Image" :disabled="uploading">
          <svg v-if="!uploading" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
          </svg>
          <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="animate-spin">
            <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
          </svg>
        </button>
      </div>
    </div>
    <editor-content :editor="editor" class="editor-content" />

    <!-- Hidden file input -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      @change="handleFileSelect"
      style="display: none"
    />
  </div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import ImageResize from 'tiptap-extension-resize-image'
import Underline from '@tiptap/extension-underline'
import Color from '@tiptap/extension-color'
import { TextStyle } from '@tiptap/extension-text-style'
import Placeholder from '@tiptap/extension-placeholder'
import { watch, ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps<{
  modelValue: string
  placeholder?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const showHeadingDropdown = ref(false)
const showColorDropdown = ref(false)
const uploading = ref(false)
const headingDropdown = ref<HTMLElement>()
const colorDropdown = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()

// Predefined color palette
const colorPalette = [
  '#000000', '#374151', '#6b7280', '#9ca3af', '#d1d5db',
  '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
  '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
  '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
  '#ec4899', '#f43f5e'
]

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit.configure({
      underline: false,
    }),
    TextStyle,
    Color,
    ImageResize,
    Underline,
    Placeholder.configure({
      placeholder: props.placeholder || '开始输入内容...',
    }),
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML())
  },
  editorProps: {
    attributes: {
      class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
    },
  },
})

const currentHeadingText = computed(() => {
  if (!editor.value) return '正文'

  if (editor.value.isActive('heading', { level: 1 })) return '标题1'
  if (editor.value.isActive('heading', { level: 2 })) return '标题2'
  if (editor.value.isActive('heading', { level: 3 })) return '标题3'
  return '正文'
})

const currentTextColor = computed(() => {
  if (!editor.value) return '#000000'
  return editor.value.getAttributes('textStyle').color || '#000000'
})

const isHeadingActive = computed(() => {
  if (!editor.value) return false
  return editor.value.isActive('heading') || editor.value.isActive('paragraph')
})

const toggleHeadingDropdown = () => {
  showHeadingDropdown.value = !showHeadingDropdown.value
  showColorDropdown.value = false
}

const toggleColorDropdown = () => {
  showColorDropdown.value = !showColorDropdown.value
  showHeadingDropdown.value = false
}

const setHeading = (level: number | 'paragraph') => {
  if (level === 'paragraph') {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    editor.value?.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run()
  }
  showHeadingDropdown.value = false
}

const setTextColor = (color: string) => {
  editor.value?.chain().focus().setColor(color).run()
  showColorDropdown.value = false
}

const removeTextColor = () => {
  editor.value?.chain().focus().unsetColor().run()
  showColorDropdown.value = false
}

const addImage = () => {
  fileInput.value?.click()
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Please select an image file')
    return
  }

  // Validate file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    alert('File size must be less than 5MB')
    return
  }

  uploading.value = true

  try {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/v1/services/upload-image', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || 'Upload failed')
    }

    const data = await response.json()
    // Use the relative URL directly - Vite proxy will handle it
    const imageUrl = data.url

    // Insert image into editor
    editor.value?.chain().focus().setImage({ src: imageUrl }).run()

  } catch (error) {
    console.error('Upload error:', error)
    alert('Failed to upload image. Please try again.')
  } finally {
    uploading.value = false
    // Reset file input
    if (target) target.value = ''
  }
}

const handleClickOutside = (event: MouseEvent) => {
  if (headingDropdown.value && !headingDropdown.value.contains(event.target as Node)) {
    showHeadingDropdown.value = false
  }
  if (colorDropdown.value && !colorDropdown.value.contains(event.target as Node)) {
    showColorDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

watch(() => props.modelValue, (value) => {
  if (editor.value && editor.value.getHTML() !== value) {
    editor.value.commands.setContent(value, { emitUpdate: false })
  }
})
</script>

<style scoped>
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.editor-toolbar button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.editor-content :deep(.ProseMirror .editor-image) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 12px 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Color dropdown specific styles */
.color-toggle {
  min-width: 48px !important;
  gap: 4px !important;
}

.color-indicator {
  width: 16px;
  height: 3px;
  border-radius: 1px;
  border: 1px solid #e5e7eb;
}

.color-menu {
  width: 200px;
  padding: 12px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.color-swatch:hover {
  transform: scale(1.1);
  border-color: #9ca3af;
}

.color-actions {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
}

.color-action {
  width: 100%;
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.color-action:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Image resize handles */
.editor-content :deep(.ProseMirror img.ProseMirror-selectednode) {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.editor-content :deep(.ProseMirror .image-resizer) {
  display: inline-block;
  position: relative;
}

.editor-content :deep(.ProseMirror .image-resizer::after) {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #3b82f6;
  cursor: nw-resize;
  border-radius: 2px;
}

/* Rest of existing styles remain the same */
.rich-editor {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #fafbfc;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  gap: 1px;
  background: #e5e7eb;
  border-radius: 6px;
  padding: 1px;
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: #d1d5db;
  margin: 0 6px;
}

.editor-toolbar button {
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.editor-toolbar button:hover {
  background: #f3f4f6;
  color: #111827;
}

.editor-toolbar button.is-active {
  background: #3b82f6;
  color: white;
}

.editor-toolbar button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Dropdown styles */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  padding: 6px 8px 6px 12px !important;
  min-width: 60px !important;
  justify-content: space-between !important;
  gap: 8px;
}

.dropdown-text {
  font-size: 14px;
  white-space: nowrap;
}

.dropdown-arrow {
  transition: transform 0.15s ease;
}

.dropdown-toggle.is-active .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  margin-top: 4px;
  overflow: hidden;
}

.dropdown-item {
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  text-align: left;
  transition: background-color 0.15s ease;
  border-radius: 0;
  min-width: auto;
  height: auto;
  justify-content: flex-start;
}

.dropdown-item:hover {
  background: #f3f4f6;
}

.dropdown-item.is-active {
  background: #eff6ff;
  color: #2563eb;
}

.dropdown-item.heading-1 {
  font-size: 18px;
  font-weight: 700;
}

.dropdown-item.heading-2 {
  font-size: 16px;
  font-weight: 600;
}

.dropdown-item.heading-3 {
  font-size: 15px;
  font-weight: 600;
}

.editor-content {
  padding: 16px 20px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

/* Editor content styling */
.editor-content :deep(.ProseMirror) {
  outline: none;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
}

.editor-content :deep(.ProseMirror h1) {
  font-size: 24px;
  font-weight: 700;
  margin: 16px 0 8px 0;
  color: #111827;
}

.editor-content :deep(.ProseMirror h2) {
  font-size: 20px;
  font-weight: 600;
  margin: 14px 0 6px 0;
  color: #111827;
}

.editor-content :deep(.ProseMirror p) {
  margin: 8px 0;
}

.editor-content :deep(.ProseMirror ul),
.editor-content :deep(.ProseMirror ol) {
  padding-left: 20px;
  margin: 8px 0;
}

.editor-content :deep(.ProseMirror li) {
  margin: 4px 0;
}

.editor-content :deep(.ProseMirror strong) {
  font-weight: 600;
}

.editor-content :deep(.ProseMirror em) {
  font-style: italic;
}

.editor-content :deep(.ProseMirror u) {
  text-decoration: underline;
}

.editor-content :deep(.ProseMirror .editor-image) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 12px 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.editor-content :deep(.ProseMirror blockquote) {
  border-left: 4px solid #e2e8f0;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #6b7280;
}

.editor-content :deep(.ProseMirror code) {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.editor-content :deep(.ProseMirror pre) {
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.editor-content :deep(.ProseMirror pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

/* Placeholder styling */
.editor-content :deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}
</style>
