<template>
  <div class="service-tag-view">

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="left-controls">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="分类名"
          @keydown.enter="searchTags"
          class="search-input"
        >
        <button @click="addTag" class="btn-primary">添加分类</button>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th class="id-col">ID</th>
            <th class="name-col">分类名称</th>
            <th class="status-col">状态</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td :colspan="4" style="text-align: center; padding: 40px;">
              正在加载...
            </td>
          </tr>
          <tr v-else-if="tags.length === 0">
            <td :colspan="4" style="text-align: center; padding: 40px;">
              没有找到分类
            </td>
          </tr>
          <tr v-else v-for="tag in tags" :key="tag.id" class="table-row">
            <td class="id-col">{{ tag.id }}</td>
            <td class="name-col">{{ tag.name }}</td>
            <td class="status-col">
              <span
                class="status-badge"
                :class="tag.is_active ? 'status-active' : 'status-inactive'"
              >
                {{ tag.is_active ? '启用' : '禁用' }}
              </span>
            </td>
            <td class="actions-col">
              <div class="action-buttons">
                <button @click="editTag(tag)" class="btn-action btn-edit">
                  编辑
                </button>
                <button @click="toggleTagStatus(tag)" class="btn-action" :class="tag.is_active ? 'btn-disable' : 'btn-enable'">
                  {{ tag.is_active ? '禁用' : '启用' }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Add/Edit Tag Modal -->
    <teleport to="body">
      <div class="modal" v-if="showTagModal">
        <div class="modal-content">
          <span class="close" @click="closeTagModal">&times;</span>
          <h2>{{ isEditing ? '编辑分类' : '添加分类' }}</h2>

          <label for="tagName">分类名称:</label>
          <input type="text" id="tagName" v-model="currentTag.name">

          <div class="modal-actions">
            <button @click="saveTag" class="btn-primary">保存</button>
            <button @click="closeTagModal" class="btn-secondary">取消</button>
          </div>
        </div>
      </div>
    </teleport>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="pagination-info">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalRecords) }} 条记录，共 {{ totalRecords }} 条
      </div>
      <div class="pagination">
        <button @click="prevPage" :disabled="currentPage === 1" class="btn-page">
          上一页
        </button>
        <span
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          class="page-number"
          :class="{ active: page === currentPage }"
        >
          {{ page }}
        </span>
        <button @click="nextPage" :disabled="currentPage === totalPages" class="btn-page">
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { watchDebounced } from '@vueuse/core'

interface ServiceTag {
  id: number
  name: string
  is_active: boolean
}

// State
const tags = ref<ServiceTag[]>([])
const isLoading = ref(true)
const searchQuery = ref('')
const showTagModal = ref(false)
const isEditing = ref(false)
const currentTag = ref({ id: 0, name: '', is_active: true })

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)

// Computed properties
const totalPages = computed(() => {
  if (totalRecords.value === 0) return 1
  return Math.ceil(totalRecords.value / pageSize.value)
})

const visiblePages = computed(() => {
  const pages: number[] = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
})

// Methods
const fetchTags = async () => {
  isLoading.value = true
  try {
    const params = new URLSearchParams({
      skip: ((currentPage.value - 1) * pageSize.value).toString(),
      limit: pageSize.value.toString()
    })

    if (searchQuery.value) params.append('search', searchQuery.value)

    const response = await fetch(`/api/v1/service-tags/all?${params.toString()}`)
    if (!response.ok) throw new Error('Network response was not ok')

    const data = await response.json()
    tags.value = data.tags || data
    totalRecords.value = data.total || tags.value.length
  } catch (error) {
    console.error('Failed to fetch tags:', error)
  } finally {
    isLoading.value = false
  }
}

const addTag = () => {
  currentTag.value = { id: 0, name: '', is_active: true }
  isEditing.value = false
  showTagModal.value = true
}

const editTag = (tag: ServiceTag) => {
  currentTag.value = { ...tag }
  isEditing.value = true
  showTagModal.value = true
}

const saveTag = async () => {
  try {
    const url = isEditing.value
      ? `/api/v1/service-tags/${currentTag.value.id}`
      : '/api/v1/service-tags/'

    const method = isEditing.value ? 'PATCH' : 'POST'

    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: currentTag.value.name })
    })

    if (response.ok) {
      closeTagModal()
      fetchTags()
    } else {
      const errorData = await response.json()
      alert(`保存失败: ${errorData.detail || '未知错误'}`)
    }
  } catch (error) {
    console.error('Error saving tag:', error)
    alert('网络错误，保存失败')
  }
}

const toggleTagStatus = async (tag: ServiceTag) => {
  const newStatus = !tag.is_active
  const actionText = newStatus ? '启用' : '禁用'

  if (confirm(`确定要${actionText}分类 "${tag.name}" 吗？`)) {
    try {
      const response = await fetch(`/api/v1/service-tags/${tag.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: newStatus })
      })

      if (response.ok) {
        tag.is_active = newStatus
      } else {
        const errorData = await response.json()
        alert(`操作失败: ${errorData.detail || '未知错误'}`)
      }
    } catch (error) {
      console.error('Error toggling tag status:', error)
      alert('网络错误，操作失败')
    }
  }
}

const closeTagModal = () => {
  showTagModal.value = false
  currentTag.value = { id: 0, name: '', is_active: true }
}

const searchTags = () => {
  currentPage.value = 1
  fetchTags()
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (page: number) => {
  currentPage.value = page
}

// Watch for pagination changes
watch([currentPage], fetchTags)

// Watch for search query changes with debounce
watchDebounced(
  searchQuery,
  () => {
    searchTags()
  },
  { debounce: 300, maxWait: 1000 }
)

// Lifecycle
onMounted(() => {
  fetchTags()
})
</script>

<style scoped>
/* Reuse styles from ServiceView.vue */
.service-tag-view {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.btn-edit {
  background-color: #ffc107;
  color: #212529;
}

.btn-enable {
  background-color: #28a745;
  color: white;
}

.btn-disable {
  background-color: #dc3545;
  color: white;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-page {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-page:hover:not(:disabled) {
  background: #f0f0f0;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  min-width: 32px;
  text-align: center;
}

.page-number:hover {
  background: #f0f0f0;
}

.page-number.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
}

.close {
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
