<template>
  <div class="service-management">

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="left-controls">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="服务名"
          @keydown.enter="searchServices"
          class="search-input"
        >
        <select v-model="tagFilter" class="filter-select">
          <option value="">所有分类</option>
          <option v-for="tag in serviceTags" :key="tag" :value="tag">
            {{ tag }}
          </option>
        </select>
        <select v-model="statusFilter" class="filter-select">
          <option value="">状态</option>
          <option value="active">启用</option>
          <option value="inactive">禁用</option>
        </select>
        <select v-model="sortOrder" class="filter-select">
          <option value="created_at_desc">创建时间降序</option>
          <option value="created_at_asc">创建时间升序</option>
          <option value="name_asc">名称升序</option>
          <option value="name_desc">名称降序</option>
          <option value="id_asc">ID升序</option>
          <option value="id_desc">ID降序</option>
        </select>
        <button @click="searchServices" class="btn btn-primary">
          <i class="icon-search"></i> 搜索
        </button>
        <button @click="addService" class="btn btn-success">
          <i class="icon-plus"></i> 添加
        </button>
      </div>
      <div class="right-controls">
        <span class="record-count">共{{ totalRecords }}条</span>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
            </th>
            <th class="id-col">序号</th>
            <th class="time-col">创建时间</th>
            <th class="title-col">服务名称</th>
            <th class="tag-col">分类</th>
            <th class="price-col">价格</th>
            <th class="status-col">状态</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td :colspan="8" style="text-align: center; padding: 40px;">
              正在加载...
            </td>
          </tr>
          <tr v-else-if="services.length === 0">
            <td :colspan="8" style="text-align: center; padding: 40px;">
              没有找到服务
            </td>
          </tr>
          <tr v-else v-for="service in services" :key="service.id" class="table-row">
            <td class="checkbox-col">
              <input type="checkbox" v-model="selectedItems" :value="service.id">
            </td>
            <td class="id-col">{{ service.id }}</td>
            <td class="time-col">{{ formatDate(service.created_at) }}</td>
            <td class="title-col">
              <div class="service-title">
                <span class="service-name">{{ service.name }}</span>
              </div>
            </td>
            <td class="tag-col">
              <span class="tag-badge">{{ service.tag }}</span>
            </td>
            <td class="price-col">
              <span class="price">¥{{ service.price }}/次</span>
            </td>

            <td class="status-col">
              <span
                class="status-badge"
                :class="service.is_active ? 'status-active' : 'status-inactive'"
              >
                {{ service.is_active ? '启用' : '禁用' }}
              </span>
            </td>
            <td class="actions-col">
              <div class="action-buttons">
                <button @click="viewService(service)" class="btn-action btn-view" title="查看">
                  详情
                </button>
                <button @click="editService(service)" class="btn-action btn-edit" title="编辑">
                  编辑
                </button>
                <button @click="toggleServiceStatus(service)" class="btn-action" :class="service.is_active ? 'btn-disable' : 'btn-enable'" :title="service.is_active ? '禁用' : '启用'">
                  {{ service.is_active ? '禁用' : '启用' }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="pagination-info">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalRecords) }} 条记录，共 {{ totalRecords }} 条
      </div>
      <div class="pagination">
        <button @click="prevPage" :disabled="currentPage === 1" class="btn-page">
          上一页
        </button>
        <span
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          class="page-number"
          :class="{ active: page === currentPage }"
        >
          {{ page }}
        </span>
        <button @click="nextPage" :disabled="currentPage === totalPages" class="btn-page">
          下一页
        </button>
      </div>
    </div>
  </div>

  <!-- View Service Modal -->
  <teleport to="body">
    <div class="modal" v-if="showViewServiceModal && currentService">
      <div class="modal-content">
        <span class="close" @click="closeViewServiceModal">&times;</span>
        <h2>服务详情</h2>
        <div class="view-details">
          <p><strong>ID:</strong> <span>{{ currentService.id }}</span></p>
          <p><strong>服务名称:</strong> <span>{{ currentService.name }}</span></p>
          <p><strong>服务描述:</strong> <span>{{ currentService.description }}</span></p>
          <p><strong>详细内容:</strong></p>
          <div class="description-content" v-html="currentService.content"></div>
          <p><strong>价格:</strong> <span>¥{{ currentService.price }}/次</span></p>
          <p><strong>分类:</strong> <span>{{ currentService.tag }}</span></p>
          <p><strong>状态:</strong> <span>{{ currentService.is_active ? '启用' : '禁用' }}</span></p>
          <p><strong>创建时间:</strong> <span>{{ formatDate(currentService.created_at) }}</span></p>
        </div>
      </div>
    </div>
  </teleport>

  <!-- Edit Service Modal -->
  <teleport to="body">
    <div class="modal" v-if="showEditServiceModal && currentService">
      <div class="modal-content large-modal">
        <span class="close" @click="closeEditServiceModal">&times;</span>
        <h2>编辑服务</h2>

        <label for="editServiceName">服务名称:</label>
        <input type="text" id="editServiceName" v-model="currentService.name">

        <label for="editServiceDescription">服务描述:</label>
        <input type="text" id="editServiceDescription" v-model="currentService.description">

        <label for="editServiceContent">详细内容:</label>
        <RichTextEditor v-model="currentService.content" />

        <label for="editServicePrice">价格:</label>
        <input type="number" id="editServicePrice" v-model="currentService.price">

        <label for="editServiceTag">服务分类:</label>
        <select id="editServiceTag" v-model="currentService.tag">
          <option v-for="tag in serviceTags" :key="tag" :value="tag">{{ tag }}</option>
        </select>

        <label>封面图:</label>
        <div class="image-upload-section">
          <div v-if="currentService.image_url" class="current-image">
            <img :src="currentService.image_url" alt="当前图片" class="preview-image">
          </div>
          <div class="image-upload-actions">
            <button type="button" @click="triggerFileInput('edit')" class="btn-file-action">选择文件</button>
            <button v-if="currentService.image_url" type="button" @click="removeServiceImage('edit')" class="btn-file-action btn-file-danger">删除图片</button>
          </div>
          <input ref="fileInputEdit" type="file" @change="handleImageUpload($event, 'edit')" accept="image/*" style="display: none;">
          <div v-if="uploadingImage" class="uploading">上传中...</div>
        </div>

        <div class="modal-actions">
          <button @click="saveEditedService" class="btn btn-primary">保存</button>
          <button type="button" @click="closeEditServiceModal" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>
  </teleport>

  <teleport to="body">
    <div class="modal" v-if="showAddServiceModal">
      <div class="modal-content large-modal">
        <span class="close" @click="closeAddServiceModal">&times;</span>
        <h2>添加服务</h2>

        <label for="serviceName">服务名称:</label>
        <input type="text" id="serviceName" v-model="newService.name">

        <label for="serviceDescription">服务描述:</label>
        <input type="text" id="serviceDescription" v-model="newService.description">

        <label for="serviceContent">详细内容:</label>
        <RichTextEditor v-model="newService.content" />

        <label for="servicePrice">价格:</label>
        <input type="number" id="servicePrice" v-model="newService.price">

        <label for="serviceTag">服务标签:</label>
        <select id="serviceTag" v-model="newService.tag">
          <option v-for="tag in serviceTags" :key="tag" :value="tag">{{ tag }}</option>
        </select>

        <label>封面图:</label>
        <div class="image-upload-section">
          <div v-if="newService.image_url" class="current-image">
            <img :src="newService.image_url" alt="预览图片" class="preview-image">
          </div>
          <div class="image-upload-actions">
            <button type="button" @click="triggerFileInput('new')" class="btn-file-action">选择文件</button>
            <button v-if="newService.image_url" type="button" @click="removeServiceImage('new')" class="btn-file-action btn-file-danger">删除图片</button>
          </div>
          <input ref="fileInputNew" type="file" @change="handleImageUpload($event, 'new')" accept="image/*" style="display: none;">
          <div v-if="uploadingImage" class="uploading">上传中...</div>
        </div>

        <div class="modal-actions">
          <button @click="saveNewService" class="btn btn-primary">保存</button>
          <button type="button" @click="closeAddServiceModal" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { watchDebounced } from '@vueuse/core';
import RichTextEditor from '@/views/components/RichTextEditor.vue';

// Types
interface Service {
  id: number
  name: string
  image_url: string
  description: string
  content: string  // Remove optional, make required
  tag: string
  price: number
  is_active: boolean
  created_at: string
}

type SortOrder = 'id_asc' | 'id_desc' | 'created_at_desc' | 'created_at_asc' | 'name_asc' | 'name_desc'
type StatusFilter = '' | 'active' | 'inactive'

// Reactive state
const searchQuery = ref<string>('')
const tagFilter = ref<string>('')
const serviceTags = ref<string[]>([])
const statusFilter = ref<StatusFilter>('')
const sortOrder = ref<SortOrder>('created_at_desc')
const selectAll = ref<boolean>(false)
const selectedItems = ref<number[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(20)
const showAddServiceModal = ref<boolean>(false)
const totalRecords = ref<number>(0)
const services = ref<Service[]>([]) // Will be populated from API
const isLoading = ref<boolean>(true) // To show loading state

const showViewServiceModal = ref<boolean>(false)
const showEditServiceModal = ref<boolean>(false)
const currentService = ref<Service | null>(null)

const newService = ref({
  name: '',
  description: '',
  content: '',  // Add content field
  price: 0,
  tag: '',
  image_url: ''
});

const uploadingImage = ref<boolean>(false)
const fileInputEdit = ref<HTMLInputElement | null>(null)
const fileInputNew = ref<HTMLInputElement | null>(null)

const saveNewService = async () => {
  try {
    const response = await fetch('/api/v1/services/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newService.value)
    });

    if (response.ok) {
      console.log('Service created successfully');
      closeAddServiceModal();
      fetchServices(); // Refresh the service list
    } else {
      console.error('Failed to create service');
    }
  } catch (error) {
    console.error('Error creating service:', error);
  }
};

// Computed properties
const totalPages = computed<number>(() => {
  if (totalRecords.value === 0) return 1
  return Math.ceil(totalRecords.value / pageSize.value)
})

const visiblePages = computed<number[]>(() => {
  const pages: number[] = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
})

// Methods
const formatDate = (dateStr: string): string => {
  if (!dateStr) return ''
  // Basic formatting, can be improved with a library like date-fns
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const fetchServices = async () => {
  isLoading.value = true
  try {
    const params = new URLSearchParams({
      skip: ((currentPage.value - 1) * pageSize.value).toString(),
      limit: pageSize.value.toString(),
      sort_order: sortOrder.value
    })

    if (searchQuery.value) params.append('searchQuery', searchQuery.value)
    if (tagFilter.value) params.append('tagFilter', tagFilter.value)
    if (statusFilter.value) params.append('statusFilter', statusFilter.value)

    const response = await fetch(`/api/v1/services/?${params.toString()}`)
    if (!response.ok) throw new Error('Network response was not ok')

    const data = await response.json()

    services.value = data.services.map((s: Service) => ({
      id: s.id,
      name: s.name,
      image_url: s.image_url,
      description: s.description,
      content: s.content || '',  // Provide default empty string
      tag: s.tag,
      price: s.price,
      is_active: s.is_active,
      created_at: s.created_at
    }))
    totalRecords.value = data.total
  } catch (error) {
    console.error('Failed to fetch services:', error)
  } finally {
    isLoading.value = false
  }
}

const fetchServiceTags = async () => {
  try {
    // For the admin panel, we fetch all tags, not just active ones.
    const response = await fetch('/api/v1/service-tags/all')
    if (!response.ok) {
      throw new Error('Network response was not ok for service tags')
    }
    // The /all endpoint returns an array of objects, so we map it to get the names.
    const tags: { name: string }[] = await response.json()
    serviceTags.value = tags.map(tag => tag.name)
    console.log('Fetched all service tags:', serviceTags.value)
  } catch (error) {
    console.error('Failed to fetch service tags:', error)
    // Optionally, set some default tags or show an error to the user
  }
}

const toggleSelectAll = (): void => {
  if (selectAll.value) {
    selectedItems.value = services.value.map(s => s.id)
  } else {
    selectedItems.value = []
  }
}

const searchServices = (): void => {
  currentPage.value = 1
  fetchServices()
}

const addService = (): void => {
  showAddServiceModal.value = true
}

const viewService = (service: Service): void => {
  currentService.value = service
  showViewServiceModal.value = true
}

const closeViewServiceModal = () => {
  showViewServiceModal.value = false
  currentService.value = null
}

const editService = (service: Service): void => {
  // Create a copy to avoid modifying the list directly while editing
  currentService.value = {
    ...service,
    content: service.content || '' // Ensure content is never undefined
  }
  showEditServiceModal.value = true
}

const closeEditServiceModal = () => {
  showEditServiceModal.value = false
  currentService.value = null
}

const saveEditedService = async () => {
  if (!currentService.value) return

  try {
    const { id, name, description, content, price, tag, image_url } = currentService.value
    const response = await fetch(`/api/v1/services/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, description, content, price, tag, image_url })
    })

    if (response.ok) {
      closeEditServiceModal()
      fetchServices()
    } else {
      const errorData = await response.json()
      alert(`更新失败: ${errorData.detail || '未知错误'}`)
    }
  } catch (error) {
    console.error('Error updating service:', error)
    alert('网络错误，更新失败')
  }
}

const toggleServiceStatus = async (service: Service): Promise<void> => {
  const newStatus = !service.is_active;
  const actionText = newStatus ? '启用' : '禁用';
  if (confirm(`确定要${actionText}服务 "${service.name}" 吗？`)) {
    try {
      const response = await fetch(`/api/v1/services/${service.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: newStatus })
      });

      if (response.ok) {
        const updatedService = await response.json();
        const index = services.value.findIndex(s => s.id === service.id);
        if (index !== -1) {
          services.value[index].is_active = updatedService.is_active;
        }
      } else {
        const errorData = await response.json();
        alert(`操作失败: ${errorData.detail || '未知错误'}`);
      }
    } catch (error) {
      console.error(`Error toggling service status:`, error);
      alert('网络错误，操作失败');
    }
  }
}


const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (page: number): void => {
  currentPage.value = page
}

const closeAddServiceModal = () => {
  showAddServiceModal.value = false;
  newService.value = { name: '', description: '', content: '', price: 0, tag: '', image_url: '' }
};

const handleImageUpload = async (event: Event, type: 'new' | 'edit') => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }

  // Validate file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    alert('文件大小不能超过5MB')
    return
  }

  uploadingImage.value = true

  try {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/v1/services/upload-image', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '上传失败')
    }

    const data = await response.json()
    const imageUrl = data.url

    if (type === 'new') {
      newService.value.image_url = imageUrl
    } else if (currentService.value) {
      currentService.value.image_url = imageUrl
    }

  } catch (error) {
    console.error('Upload error:', error)
    alert('图片上传失败，请重试')
  } finally {
    uploadingImage.value = false
    // Reset file input
    if (target) target.value = ''
  }
}

const triggerFileInput = (type: 'new' | 'edit') => {
  if (type === 'new') {
    fileInputNew.value?.click()
  } else {
    fileInputEdit.value?.click()
  }
}

const removeServiceImage = (type: 'new' | 'edit') => {
  if (type === 'new') {
    newService.value.image_url = ''
  } else if (currentService.value) {
    currentService.value.image_url = ''
  }
}

// Watch for changes in filters, sorting, and pagination
watch([currentPage, sortOrder, tagFilter, statusFilter], fetchServices);

// Watch for search query changes with a debounce for a smoother user experience
watchDebounced(
  searchQuery,
  () => {
    searchServices();
  },
  // Debounce for 300ms, but wait no more than 1000ms
  { debounce: 300, maxWait: 1000 }
);

// Lifecycle
onMounted(() => {
  fetchServices()
  fetchServiceTags()
})
</script>


<style scoped>
/* Basic modal styles */
/* Basic modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
}

.close {
  position: absolute;
  right: 10px;
  top: 0;
  font-size: 20px;
  cursor: pointer;
}

/* Form Styles */
.modal-content label {
  display: block;
  margin-top: 10px;
  font-weight: bold;
}

.modal-content input[type="text"],
.modal-content input[type="number"],
.modal-content textarea,
.modal-content select {
  width: 100%;
  padding: 8px;
  margin-top: 5px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding is included in the element's total width and height */
}

.modal-content textarea {
  height: 100px;
}

.modal-content button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.modal-content button:hover {
  background-color: #367c39;
}

/* View Modal Details */
.view-details p {
  margin: 10px 0;
  font-size: 14px;
  line-height: 1.6;
  display: flex;
  align-items: flex-start;
}
.view-details p strong {
  font-weight: 600;
  color: #555;
  margin-right: 8px;
  display: inline-block;
  width: 100px;
  flex-shrink: 0;
}
.view-details p span {
  flex: 1;
}

.service-management {
  padding: 20px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input, .filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input {
  width: 150px;
}

.filter-select {
  width: 150px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-success {
  background: #67c23a;
  color: white;
}

.btn-info {
  background: #909399;
  color: white;
}

.btn-secondary {
  background: white;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.btn:hover {
  opacity: 0.9;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.record-count {
  color: #666;
  font-size: 14px;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 12px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.table-row:hover {
  background: #f8f9fa;
}

.checkbox-col { width: 40px; }
.id-col { width: 60px; }
.time-col { width: 140px; }
.title-col { width: 300px; }
.tag-col { width: 100px; }
.price-col { width: 100px; }
.rating-col { width: 120px; }
.views-col { width: 80px; }
.status-col { width: 80px; }
.actions-col { width: 220px; }

.service-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service-name {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;
}

.service-name:hover {
  text-decoration: underline;
}

.service-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  width: fit-content;
}

.tag-default { background: #909399; }

.tag-badge {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  background: #f0f0f0;
  color: #666;
}

.price {
  font-weight: 600;
  color: #f56c6c;
}

.rating {
  display: flex;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 14px;
}

.star.active {
  color: #ffc107;
}

.status-badge {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status-active {
  background: #f0f9ff;
  color: #67c23a;
}

.status-inactive {
  background: #f5f5f5;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 6px;
}

.btn-action {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  color: white;
}

.btn-secondary:hover {
  background: #f2f6fc;
  color: #409eff;
  border-color: #c6e2ff;
  opacity: 1;
}

.btn-view { background: #409eff; }
.btn-edit { background: #909399; }
.btn-enable { background: #67c23a; }
.btn-disable { background: #e6a23c; }
.btn-delete { background: #f56c6c; }

.btn-action:hover {
  opacity: 0.8;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-page {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-page:hover:not(:disabled) {
  background: #f0f0f0;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  min-width: 32px;
  text-align: center;
}

.page-number:hover {
  background: #f0f0f0;
}

.page-number.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* Icons (using simple text alternatives) */
.icon-search:before { content: '🔍'; }
.icon-plus:before { content: '+'; }
.icon-download:before { content: '↓'; }
.icon-refresh:before { content: '↻'; }

.icon-settings:before { content: '⚙'; }
.large-modal {
  width: 90%;
  max-width: 800px;
}

/* In view details, render HTML content */
.view-details .description-content {
  max-height: 200px;
  overflow-y: auto;
}

.image-upload-section {
  margin-top: 5px;
  margin-bottom: 20px;
}

.current-image {
  margin-bottom: 10px;
}

.preview-image {
  max-width: 200px;
  max-height: 150px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.image-upload-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn-file-action {
  padding: 5px 10px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}
.btn-file-action:hover {
  background: #f2f6fc;
  color: #409eff;
  border-color: #c6e2ff;
}
.btn-file-action.btn-file-danger {
  background: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}
.btn-file-action.btn-file-danger:hover {
  background: #f56c6c;
  color: white;
}

.uploading {
  color: #409eff;
  font-size: 14px;
}
</style>
