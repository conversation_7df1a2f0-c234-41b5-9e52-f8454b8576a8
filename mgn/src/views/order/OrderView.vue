<template>
  <div class="order-management">

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="left-controls">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="订单号, 就诊人, 服务"
          @keydown.enter="searchOrders"
          class="search-input"
        >
        <select v-model="statusFilter" class="filter-select">
          <option value="">所有状态</option>
          <option v-for="status in orderStatuses" :key="status.value" :value="status.value">
            {{ status.label }}
          </option>
        </select>
        <select v-model="sortOrder" class="filter-select">
          <option value="created_at_desc">创建时间降序</option>
          <option value="created_at_asc">创建时间升序</option>
          <option value="amount_desc">金额降序</option>
          <option value="amount_asc">金额升序</option>
          <option value="id_desc">ID降序</option>
          <option value="id_asc">ID升序</option>
        </select>
        <button @click="searchOrders" class="btn btn-primary">
          <i class="icon-search"></i> 搜索
        </button>
        <button @click="fetchOrders" class="btn btn-info">
          <i class="icon-refresh"></i> 刷新
        </button>
      </div>
      <div class="right-controls">
        <span class="record-count">共{{ totalRecords }}条</span>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
            </th>
            <th class="id-col">序号</th>
            <th class="order-num-col">订单号</th>
            <th class="time-col">创建时间</th>
            <th class="service-col">服务项目</th>
            <th class="patient-col">就诊人</th>
            <th class="nurses-col">护士</th>
            <th class="amount-col">订单金额</th>
            <th class="status-col">订单状态</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td :colspan="9" style="text-align: center; padding: 40px;">
              正在加载...
            </td>
          </tr>
          <tr v-else-if="orders.length === 0">
            <td :colspan="9" style="text-align: center; padding: 40px;">
              没有找到订单
            </td>
          </tr>
          <tr v-else v-for="order in orders" :key="order.id" class="table-row">
            <td class="checkbox-col">
              <input type="checkbox" v-model="selectedItems" :value="order.id">
            </td>
            <td class="id-col">{{ order.id }}</td>
            <td class="order-num-col">{{ order.order_num }}</td>
            <td class="time-col">{{ formatDate(order.created_at) }}</td>
            <td class="service-col">{{ order.service.name }}</td>
            <td class="patient-col">{{ order.patient.name }}</td>
            <td class="nurses-col">
              <div class="nurse-assignment">
                <div v-if="order.primary_nurse" class="nurse-info">
                  <span class="nurse-badge primary">主: {{ order.primary_nurse.name }}</span>
                </div>
                <div v-if="order.secondary_nurse" class="nurse-info">
                  <span class="nurse-badge secondary">副: {{ order.secondary_nurse.name }}</span>
                </div>
                <div v-if="!order.primary_nurse && !order.secondary_nurse" class="no-nurses">
                  未分配
                </div>
              </div>
            </td>
            <td class="amount-col">
              <span class="price">¥{{ order.amount.toFixed(2) }}</span>
            </td>
            <td class="status-col">
              <span class="status-badge" :class="`status-${getStatusClass(order.status)}`">
                {{ statusToChinese(order.status) }}
              </span>
            </td>
            <td class="actions-col">
              <div class="action-buttons">
                <button @click="viewOrder(order)" class="btn-action btn-view" title="查看">
                  详情
                </button>
                <button
                  v-if="order.status === 'PAID'"
                  @click="assignNurses(order)"
                  class="btn-action btn-assign"
                  title="分配护士"
                >
                  派单
                </button>
                <button
                  v-if="order.status === 'PAID' || order.status === 'PROCESSING'"
                  @click="completeOrder(order)"
                  class="btn-action btn-complete"
                  title="完成订单"
                >
                  完成
                </button>
                <button
                  v-if="order.status === 'PAID' || order.status === 'REFUNDING'"
                  @click="refundOrder(order)"
                  class="btn-action btn-refund"
                  title="退款"
                >
                  退款
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="pagination-info">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalRecords) }} 条记录，共 {{ totalRecords }} 条
      </div>
      <div class="pagination">
        <button @click="prevPage" :disabled="currentPage === 1" class="btn-page">
          上一页
        </button>
        <span
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          class="page-number"
          :class="{ active: page === currentPage }"
        >
          {{ page }}
        </span>
        <button @click="nextPage" :disabled="currentPage === totalPages" class="btn-page">
          下一页
        </button>
      </div>
    </div>
  </div>

  <!-- Assign Nurses Modal -->
  <teleport to="body">
    <div class="modal" v-if="showAssignModal && currentOrder">
      <div class="modal-content">
        <span class="close" @click="closeAssignModal">&times;</span>
        <h2>分配护士 - {{ currentOrder.order_num }}</h2>
        <div class="assign-form">
          <div class="form-group">
            <label>主护士:</label>
            <select v-model="assignmentData.primary_nurse_id" class="nurse-select">
              <option value="">请选择主护士</option>
              <option v-for="nurse in availableNurses" :key="nurse.id" :value="nurse.id">
                {{ nurse.name }} - {{ nurse.department }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>副护士:</label>
            <select v-model="assignmentData.secondary_nurse_id" class="nurse-select">
              <option value="">请选择副护士</option>
              <option v-for="nurse in availableNurses" :key="nurse.id" :value="nurse.id"
                      :disabled="nurse.id === assignmentData.primary_nurse_id">
                {{ nurse.name }} - {{ nurse.department }}
              </option>
            </select>
          </div>
          <div class="form-actions">
            <button @click="saveNurseAssignment" class="btn btn-primary" :disabled="isAssigning">
              {{ isAssigning ? '保存中...' : '保存' }}
            </button>
            <button @click="closeAssignModal" class="btn btn-secondary">取消</button>
          </div>
        </div>
      </div>
    </div>
  </teleport>

  <!-- View Order Modal -->
  <teleport to="body">
    <div class="modal" v-if="showViewOrderModal && currentOrder">
      <div class="modal-content">
        <span class="close" @click="closeViewOrderModal">&times;</span>
        <h2>订单详情</h2>
        <div class="view-details">
          <p><strong>ID:</strong> <span>{{ currentOrder.id }}</span></p>
          <p><strong>订单号:</strong> <span>{{ currentOrder.order_num }}</span></p>
          <p><strong>服务项目:</strong> <span>{{ currentOrder.service.name }}</span></p>
          <p><strong>就诊人:</strong> <span>{{ currentOrder.patient.name }}</span></p>
          <p><strong>订单金额:</strong> <span>¥{{ currentOrder.amount.toFixed(2) }}</span></p>
          <p><strong>订单状态:</strong> <span>{{ currentOrder.status }}</span></p>
          <p><strong>预约时间:</strong> <span>{{ currentOrder.service_date }} {{ currentOrder.service_time }}</span></p>
          <p><strong>创建时间:</strong> <span>{{ formatDate(currentOrder.created_at) }}</span></p>
          <p v-if="currentOrder.paid_at"><strong>支付时间:</strong> <span>{{ formatDate(currentOrder.paid_at) }}</span></p>
          <p v-if="currentOrder.completed_at"><strong>完成时间:</strong> <span>{{ formatDate(currentOrder.completed_at) }}</span></p>
          <p v-if="currentOrder.transaction_id"><strong>交易号:</strong> <span>{{ currentOrder.transaction_id }}</span></p>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { watchDebounced } from '@vueuse/core';
import apiClient from '@/services/api';

// Types
interface ServiceInfo {
  name: string;
}

interface PatientInfo {
  name: string;
}

interface NurseInfo {
  id: number;
  name: string;
  department: string;
}

interface Order {
  id: number;
  order_num: string;
  service: ServiceInfo;
  patient: PatientInfo;
  amount: number;
  status: string;
  created_at: string;
  service_date: string;
  service_time: string;
  paid_at?: string;
  completed_at?: string;
  transaction_id?: string;
  primary_nurse?: NurseInfo;
  secondary_nurse?: NurseInfo;
}

type SortOrder = 'id_asc' | 'id_desc' | 'created_at_desc' | 'created_at_asc' | 'amount_asc' | 'amount_desc';

// Reactive state
const searchQuery = ref<string>('');
const statusFilter = ref<string>('');
const sortOrder = ref<SortOrder>('created_at_desc');
const selectAll = ref<boolean>(false);
const selectedItems = ref<number[]>([]);
const currentPage = ref<number>(1);
const pageSize = ref<number>(20);
const totalRecords = ref<number>(0);
const orders = ref<Order[]>([]);
const isLoading = ref<boolean>(true);

const showViewOrderModal = ref<boolean>(false);
const currentOrder = ref<Order | null>(null);

const orderStatuses = ref([
  { value: 'UNPAID', label: '待支付' },
  { value: 'PAID', label: '已支付' },
  { value: 'FAILED', label: '支付失败' },
  { value: 'CANCELED', label: '已取消' },
  { value: 'PROCESSING', label: '处理中' },
  { value: 'COMPLETED', label: '已完成' },
  { value: 'REFUNDING', label: '退款中' },
  { value: 'REFUNDED', label: '已退款' },
  { value: 'EXPIRED', label: '已过期' },
  { value: 'CLOSED', label: '已关闭' },
  { value: 'DELETED', label: '已删除' },
]);

// Add to reactive state
const showAssignModal = ref<boolean>(false);
const availableNurses = ref<NurseInfo[]>([]);
const isAssigning = ref<boolean>(false);
const assignmentData = ref<{
  primary_nurse_id: number | string;
  secondary_nurse_id: number | string;
}>({
  primary_nurse_id: '',
  secondary_nurse_id: ''
});

// Add to reactive state
const isCompleting = ref<boolean>(false);
const isRefunding = ref<boolean>(false);

// Computed properties
const totalPages = computed<number>(() => {
  if (totalRecords.value === 0) return 1;
  return Math.ceil(totalRecords.value / pageSize.value);
});

const visiblePages = computed<number[]>(() => {
  const pages: number[] = [];
  const start = Math.max(1, currentPage.value - 2);
  const end = Math.min(totalPages.value, start + 4);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// Methods
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const statusToChinese = (status: string): string => {
  const found = orderStatuses.value.find(s => s.value === status);
  return found ? found.label : status;
};

const getStatusClass = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'UNPAID': 'unpaid',
    'PAID': 'paid',
    'FAILED': 'failed',
    'CANCELED': 'canceled',
    'PROCESSING': 'processing',
    'COMPLETED': 'completed',
    'REFUNDING': 'refunding',
    'REFUNDED': 'refunded',
    'EXPIRED': 'expired',
    'CLOSED': 'closed',
    'DELETED': 'default', // Or some other class
  };
  return statusMap[status] || 'default';
};

const fetchOrders = async () => {
  isLoading.value = true;
  try {
    const params = new URLSearchParams({
      skip: ((currentPage.value - 1) * pageSize.value).toString(),
      limit: pageSize.value.toString(),
      sort_order: sortOrder.value,
    });

    if (searchQuery.value) params.append('search', searchQuery.value);
    if (statusFilter.value) params.append('status', statusFilter.value);

    const response = await apiClient.get(`/orders/?${params.toString()}`);
    orders.value = response.data.orders;
    totalRecords.value = response.data.total;
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    orders.value = [];
    totalRecords.value = 0;
  } finally {
    isLoading.value = false;
  }
};

const toggleSelectAll = (): void => {
  if (selectAll.value) {
    selectedItems.value = orders.value.map(o => o.id);
  } else {
    selectedItems.value = [];
  }
};

const searchOrders = (): void => {
  currentPage.value = 1;
  fetchOrders();
};

const viewOrder = (order: Order): void => {
  currentOrder.value = order;
  showViewOrderModal.value = true;
};

const closeViewOrderModal = () => {
  showViewOrderModal.value = false;
  currentOrder.value = null;
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page: number): void => {
  currentPage.value = page;
};

// Add methods
const assignNurses = async (order: Order): Promise<void> => {
  currentOrder.value = order;
  assignmentData.value = {
    primary_nurse_id: order.primary_nurse?.id || '',
    secondary_nurse_id: order.secondary_nurse?.id || ''
  };

  await fetchAvailableNurses();
  showAssignModal.value = true;
};

const fetchAvailableNurses = async (): Promise<void> => {
  try {
    const response = await apiClient.get('/nurses/available');
    availableNurses.value = response.data;
  } catch (error) {
    console.error('Failed to fetch nurses:', error);
    availableNurses.value = [];
  }
};

const saveNurseAssignment = async (): Promise<void> => {
  if (!currentOrder.value) return;

  isAssigning.value = true;
  try {
    const payload: { primary_nurse_id: number | null; secondary_nurse_id: number | null } = { primary_nurse_id: null, secondary_nurse_id: null };

    if (assignmentData.value.primary_nurse_id) {
      payload.primary_nurse_id = parseInt(assignmentData.value.primary_nurse_id as string);
    } else {
      payload.primary_nurse_id = null;
    }

    if (assignmentData.value.secondary_nurse_id) {
      payload.secondary_nurse_id = parseInt(assignmentData.value.secondary_nurse_id as string);
    } else {
      payload.secondary_nurse_id = null;
    }

    await apiClient.patch(`/orders/${currentOrder.value.id}/assign-nurses`, payload);

    // Refresh orders to show updated assignments
    await fetchOrders();
    closeAssignModal();
  } catch (error) {
    console.error('Failed to assign nurses:', error);
  } finally {
    isAssigning.value = false;
  }
};

const closeAssignModal = (): void => {
  showAssignModal.value = false;
  currentOrder.value = null;
  assignmentData.value = {
    primary_nurse_id: '',
    secondary_nurse_id: ''
  };
};

// Add methods
const completeOrder = async (order: Order): Promise<void> => {
  if (confirm(`确定要完成订单 ${order.order_num} 吗？`)) {
    isCompleting.value = true;
    try {
      await apiClient.patch(`/orders/${order.id}`, { status: 'COMPLETED' });
      await fetchOrders();
    } catch (error: unknown) {
      console.error('Failed to complete order:', error);
      const axiosError = error as {
        response?: {
          status?: number;
          data?: {
            detail?: string;
          };
        };
      };

      // Check if it's a status conflict error
      if (axiosError.response?.status === 400 && axiosError.response?.data?.detail) {
        const detail = axiosError.response.data.detail;
        if (detail.includes('Cannot complete order with status:')) {
          alert('操作失败：订单状态已发生变化，请刷新页面查看最新状态');
        } else {
          alert(`完成订单失败：${detail}`);
        }
      } else {
        alert('完成订单失败');
      }
    } finally {
      isCompleting.value = false;
    }
  }
};

const refundOrder = async (order: Order): Promise<void> => {
  const isProcessingRefund = order.status === 'REFUNDING';
  const action = isProcessingRefund ? '处理退款' : '申请退款';

  if (confirm(`确定要${action}订单 ${order.order_num} 吗？`)) {
    isRefunding.value = true;
    try {
      const status = isProcessingRefund ? 'REFUNDED' : 'REFUNDING';
      await apiClient.patch(`/orders/${order.id}`, { status });
      await fetchOrders();
    } catch (error: unknown) {
      console.error('Failed to refund order:', error);
      const axiosError = error as {
        response?: {
          status?: number;
          data?: {
            detail?: string;
          };
        };
      };

      // Check if it's a status conflict error
      if (axiosError.response?.status === 400 && axiosError.response?.data?.detail) {
        const detail = axiosError.response.data.detail;
        if (detail.includes('Cannot') && detail.includes('with status:')) {
          alert('操作失败：订单状态已发生变化，请刷新页面查看最新状态');
        } else {
          alert(`退款操作失败：${detail}`);
        }
      } else {
        alert('退款操作失败');
      }
    } finally {
      isRefunding.value = false;
    }
  }
};

// Watchers
watch([currentPage, sortOrder, statusFilter], fetchOrders);

watchDebounced(
  searchQuery,
  () => {
    searchOrders();
  },
  { debounce: 300, maxWait: 1000 }
);

// Lifecycle
onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
/* Reusing styles from ServiceView.vue and adapting for OrderView */
.order-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input, .filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 150px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary { background: #409eff; color: white; }
.btn-info { background: #909399; color: white; }
.btn:hover { opacity: 0.9; }

.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.record-count {
  color: #666;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 12px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.table-row:hover {
  background: #f8f9fa;
}

/* Column widths */
.checkbox-col { width: 40px; }
.id-col { width: 40px; }
.order-num-col { width: 180px; }
.time-col { width: 160px; }
.service-col { flex-grow: 1; }
.patient-col { width: 100px; }
.nurses-col { width: 150px; }
.amount-col { width: 100px; }
.status-col { width: 100px; }
.actions-col { width: 200px; }

.price {
  font-weight: 600;
  color: #f56c6c;
}

.status-badge {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}

.status-unpaid { background-color: #fff2e8; color: #faad14; }
.status-paid { background-color: #f6ffed; color: #52c41a; }
.status-failed { background-color: #fff2f0; color: #ff4d4f; }
.status-canceled { background-color: #f5f5f5; color: #999; }
.status-processing { background-color: #e6f7ff; color: #1890ff; }
.status-completed { background-color: #f6ffed; color: #52c41a; }
.status-refunding { background-color: #fff2e8; color: #faad14; }
.status-refunded { background-color: #f5f5f5; color: #999; }
.status-expired { background-color: #f5f5f5; color: #999; }
.status-closed { background-color: #f5f5f5; color: #999; }
.status-default { background-color: #f0f0f0; color: #666; }

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.btn-action {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  color: white;
}

.btn-view { background: #409eff; }
.btn-assign { background: #52c41a; }
.btn-complete {
  background: #52c41a;
  color: white;
}

.btn-refund {
  background: #faad14;
  color: white;
}

.btn-action:hover { opacity: 0.8; }

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-page {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-page:hover:not(:disabled) {
  background: #f0f0f0;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  min-width: 32px;
  text-align: center;
}

.page-number:hover {
  background: #f0f0f0;
}

.page-number.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  position: relative;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.close {
  position: absolute;
  right: 15px;
  top: 10px;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #aaa;
}
.close:hover {
  color: #333;
}

.modal-content h2 {
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.view-details p {
  margin: 12px 0;
  font-size: 14px;
  line-height: 1.7;
  display: flex;
  align-items: flex-start;
}
.view-details p strong {
  font-weight: 600;
  color: #555;
  margin-right: 8px;
  display: inline-block;
  width: 100px;
  flex-shrink: 0;
  text-align: right;
}
.view-details p span {
  flex: 1;
  word-break: break-all;
}

/* Icons */
.icon-search:before { content: '🔍'; }
.icon-refresh:before { content: '↻'; }

/* Add to existing styles */
.nurse-assignment {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nurse-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
  display: inline-block;
}

.nurse-badge.primary {
  background-color: #e6f7ff;
  color: #1890ff;
}

.nurse-badge.secondary {
  background-color: #f6ffed;
  color: #52c41a;
}

.no-nurses {
  color: #999;
  font-size: 12px;
}

.assign-form {
  padding: 20px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.nurse-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn-secondary {
  background: #d9d9d9;
  color: #333;
}
</style>
