<script setup lang="ts">
import NaviPane from '@/views/NaviPane.vue';
import HeaderBar from '@/views/HeaderBar.vue';
import { useLayoutStore } from '@/stores/layout';
import { storeToRefs } from 'pinia';

const layoutStore = useLayoutStore();
const { isCollapse } = storeToRefs(layoutStore);
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-aside :width="isCollapse ? '64px' : '180px'">
        <NaviPane />
      </el-aside>
      <el-container direction="vertical">
        <HeaderBar />
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped>
.common-layout {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #545c64;
  transition: width 0.3s;
  overflow-x: hidden;
}

.el-main {
  background-color: #E9EEF3;
  color: #333;
  padding: 20px;
}
</style>
