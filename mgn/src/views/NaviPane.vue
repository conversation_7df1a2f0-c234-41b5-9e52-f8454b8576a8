<template>

  <el-menu
    class="el-menu"
    :default-active="activeIndex"
    :collapse="isCollapse"
    :router="true"
  >
    <template v-for="item in menuItems" :key="item.index">

      <!-- <el-menu-item :index="item.index">
        <el-icon>
          <component :is="item.icon" />
        </el-icon>
        <template #title>{{ item.title }}</template>
      </el-menu-item> -->

      <!-- 有子菜单的情况 -->
      <el-sub-menu v-if="item.children" :index="item.index">
        <template #title>
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.title }}</span>
        </template>
        <el-menu-item v-for="child in item.children" :key="child.index" :index="child.index">
          <el-icon :size="12" style="width: 12px;">
            <component :is="child.icon" />
          </el-icon>
          <template #title>{{ child.title }}</template>
        </el-menu-item>
      </el-sub-menu>

      <!-- 没有子菜单的情况 -->
      <el-menu-item v-else :index="item.index">
        <el-icon>
          <component :is="item.icon" />
        </el-icon>
        <template #title>{{ item.title }}</template>
      </el-menu-item>

    </template>
  </el-menu>

</template>

<script lang="ts" setup>
import { h, computed, watch } from 'vue'
import { useRoute } from 'vue-router';
import * as Icons from '@element-plus/icons-vue';
import { useLayoutStore } from '@/stores/layout';
import { storeToRefs } from 'pinia';
import type { Component } from 'vue'

interface MenuItem {
  index: string;
  title: string;
  icon?: Component|string;
  children?: MenuItem[];
}

const route = useRoute();
const layoutStore = useLayoutStore();
const { isCollapse } = storeToRefs(layoutStore);
const { setBreadcrumbs } = layoutStore;
const activeIndex = computed(() => route.path);

const transparentIcon = (): Component => {
  return {
    render() {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '1em',
          height: '1em',
          opacity: '0',
          visibility: 'hidden'
        }
      })
    }
  }
}

const getIconComponent = (item: MenuItem): Component => {
  if (!item.icon) return transparentIcon();
  return (Icons as Record<string, Component>)[item.icon as string] || transparentIcon();
}

const rawMenuItems: MenuItem[] = [
  { index: '/main/dashboard', title: '首页', icon: 'HomeFilled' },
  { index: '/main/staff', title: '医护人员', icon: 'UserFilled' },
  {
    index: '/main/services', title: '服务项目', icon: 'Briefcase', children: [
      { index: '/main/services/items', title: '项目管理' },
      { index: '/main/services/tags', title: '分类管理' }
    ]
  },
  { index: '/main/orders', title: '患者订单', icon: 'List' },
  {
    index: '/main/settings', title: '系统设置', icon: 'Platform', children: [
      { index: '/main/settings/params', title: '系统参数' },
      { index: '/main/settings/history', title: '历史记录' }]
  },
];

const menuItems: MenuItem[] = rawMenuItems.map(item => ({
  ...item,
  icon: getIconComponent(item),
  children: item.children?.map(child => ({
    ...child,
    icon: getIconComponent(child)
  }))
}));

const findMenuPath = (items: MenuItem[], index: string, path: MenuItem[] = []): MenuItem[] => {
  for (const item of items) {
    if (item.index === index) {
      return [...path, item];
    }
    if (item.children) {
      const found = findMenuPath(item.children, index, [...path, item]);
      if (found.length) return found;
    }
  }
  return [];
};

watch(
  () => route.path,
  (newPath) => {
    const path = findMenuPath(rawMenuItems, newPath);
    setBreadcrumbs(path);
  },
  { immediate: true }
);

</script>

<style scoped>

.el-menu {
  --el-menu-bg-color: #545c64;
  --el-menu-text-color: #fff;
  --el-menu-active-color: #ffd04b;
  --el-menu-hover-bg-color: #434a50;

  border: none;
  flex: 1;
}
</style>
