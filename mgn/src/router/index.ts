import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import MainPage from '@/views/MainPage.vue'
import LoginView from '@/views/auth/LoginView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: LoginView,
      meta: { requiresGuest: true }
    },
    {
      path: '/main',
      component: MainPage,
      redirect: '/main/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/dashboard/DashboardView.vue'),
          meta: { title: '首页' },
        },
        {
          path: 'staff',
          name: 'Staff',
          component: () => import('@/views/staff/StaffView.vue'),
          meta: { title: '医护人员' },
        },
        {
          path: 'services',
          name: 'Services',
          redirect: '/main/services/items',
          meta: { title: '服务项目' },
          children: [
            {
              path: 'items',
              name: 'ServiceItems',
              component: () => import('@/views/service/ServiceView.vue'),
              meta: { title: '项目管理' },
            },
            {
              path: 'tags',
              name: 'ServiceTags',
              component: () => import('@/views/service/ServiceTagView.vue'),
              meta: { title: '分类管理' },
            },
          ],
        },
        {
          path: 'orders',
          name: 'Orders',
          component: () => import('@/views/order/OrderView.vue'),
          meta: { title: '患者订单' },
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/setting/SettingsLayout.vue'),
          redirect: '/settings/params',
          children: [
            {
              path: 'params',
              name: 'SystemParams',
              component: () => import('@/views/setting/AboutView.vue'),
              meta: { title: '系统参数' },
            },
            {
              path: 'history',
              name: 'History',
              component: () => import('@/views/setting/AboutView.vue'),
              meta: { title: '历史记录' },
            },
          ],
        },
      ],
    },
  ],
})

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
  } else if (to.meta.requiresGuest && authStore.isLoggedIn) {
    next('/main/dashboard')
  } else {
    next()
  }
})

export default router
