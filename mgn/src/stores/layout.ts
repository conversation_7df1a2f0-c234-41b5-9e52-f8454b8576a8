import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useLayoutStore = defineStore('layout', () => {
  const isCollapse = ref(false)
  const breadcrumbs = ref<Array<{ title: string }>>([])

  function toggleCollapse() {
    isCollapse.value = !isCollapse.value
  }

  function setBreadcrumbs(path: Array<{ title: string }>) {
    breadcrumbs.value = path
  }

  return { isCollapse, breadcrumbs, toggleCollapse, setBreadcrumbs }
})

