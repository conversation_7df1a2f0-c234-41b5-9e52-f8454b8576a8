import { defineStore } from 'pinia'
import { ref } from 'vue'

interface User {
  id: number
  username: string
  is_admin: boolean
}

interface LoginResponse {
  token: string
  user_id: number
  is_admin: boolean
  user: User
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const user = ref<User | null>(null)
  const isLoggedIn = ref(!!token.value)

  const login = async (username: string, password: string) => {
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || '登录失败')
      }

      const data: LoginResponse = await response.json()
      
      if (!data.is_admin) {
        throw new Error('您没有管理员权限')
      }

      token.value = data.token
      user.value = data.user
      isLoggedIn.value = true

      localStorage.setItem('admin_token', data.token)
      localStorage.setItem('admin_user', JSON.stringify(data.user))

    } catch (error) {
      throw error
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    isLoggedIn.value = false
    
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
  }

  const initAuth = () => {
    const savedToken = localStorage.getItem('admin_token')
    const savedUser = localStorage.getItem('admin_user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
      isLoggedIn.value = true
    }
  }

  return {
    token,
    user,
    isLoggedIn,
    login,
    logout,
    initAuth
  }
})