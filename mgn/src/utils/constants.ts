// Staff Status Constants
export const STAFF_STATUS = {
  ACTIVE: '已审核',
  INACTIVE: '待审核'
} as const

export type StaffStatusType = typeof STAFF_STATUS[keyof typeof STAFF_STATUS]

// Staff Status Mapping for API
export const STAFF_STATUS_API_MAP = {
  active: STAFF_STATUS.ACTIVE,
  inactive: STAFF_STATUS.INACTIVE
} as const

// Staff Status Class Mapping
export const STAFF_STATUS_CLASS_MAP = {
  [STAFF_STATUS.ACTIVE]: 'status-active',
  [STAFF_STATUS.INACTIVE]: 'status-inactive'
} as const

/**
 * Gets the display text for staff status
 */
export const getStaffStatusText = (isActive: boolean): string => {
  return isActive ? STAFF_STATUS.ACTIVE : STAFF_STATUS.INACTIVE
}

/**
 * Gets the CSS class for staff status
 */
export const getStaffStatusClass = (isActive: boolean): string => {
  const status = getStaffStatusText(isActive) as keyof typeof STAFF_STATUS_CLASS_MAP
  return STAFF_STATUS_CLASS_MAP[status]
}

/**
 * Gets the action text for toggling staff status
 */
export const getStaffToggleActionText = (isActive: boolean): string => {
  return isActive ? `撤销审核` : `通过审核`
}
