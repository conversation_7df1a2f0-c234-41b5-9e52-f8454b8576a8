import axios from 'axios';
import { ElNotification } from 'element-plus';
import router from '@/router';
import { useAuthStore } from '@/stores/auth'

// Create an axios instance
const apiClient = axios.create({
  baseURL: '/api/v1', // Matches the proxy in vite.config.ts
  timeout: 10000, // 10-second timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to automatically add the auth token to every request
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token') || localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for global error handling
apiClient.interceptors.response.use(
  (response) => {
    // Any status code within the range of 2xx will trigger this function
    return response;
  },
  (error) => {
    // Any status codes outside the range of 2xx will trigger this function
    let message = 'An unknown error occurred';
    if (error.response) {
      // The request was made and the server responded with a status code
      message = error.response.data?.detail || `Error: ${error.response.status}`;
      if (error.response.status === 401) {
        // Handle unauthorized access, e.g., redirect to login page
        // You might want to add router logic here: router.push('/login');
        const authStore = useAuthStore();
        authStore.logout(); // This should clear the store state
        localStorage.removeItem('token');
        router.push('/login');
        message = 'Unauthorized access. Please log in again.';
      }
    } else if (error.request) {
      // The request was made but no response was received
      message = 'No response from server. Please check your network connection.';
    } else {
      // Something happened in setting up the request that triggered an Error
      message = error.message;
    }

    ElNotification({
      title: 'API Error',
      message: message,
      type: 'error',
      duration: 5000,
    });

    return Promise.reject(error);
  }
);

// --- API Functions ---

export interface DashboardStats {
  completed_orders: number;
  processing_orders: number;
  todays_orders: number;
  total_revenue: number;
}

export interface ServiceTimeHeatmapData {
  day: string;
  hour: number;
  count: number;
}

/**
 * Fetches dashboard statistics from the server.
 */
export const getSummaryStats = async (): Promise<DashboardStats> => {
  const response = await apiClient.get<DashboardStats>('/orders/stats/summary');
  return response.data;
};

export const getDailyOrderStats = async (days: number = 30, status_filter?: string, date_field?: 'created_at' | 'service_date') => {
  const params: { days: number; status_filter?: string; date_field?: string } = { days };
  if (status_filter) {
    params.status_filter = status_filter;
  }
  if (date_field) {
    params.date_field = date_field;
  }
  const response = await apiClient.get('/orders/stats/daily-orders', {
    params
  });
  return response.data;
};

export const getServiceTypeStats = async () => {
  const response = await apiClient.get('/orders/stats/service-types');
  return response.data;
};

export const getServiceTimeHeatmap = async (): Promise<ServiceTimeHeatmapData[]> => {
  const response = await apiClient.get<ServiceTimeHeatmapData[]>('/orders/stats/service-time-heatmap');
  return response.data;
};

export default apiClient;
