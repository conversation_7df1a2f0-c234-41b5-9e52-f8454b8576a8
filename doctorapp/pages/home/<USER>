<template>
  <view class="home-container">
    <!-- Header with greeting -->
    <view class="header-section">
      <view class="greeting">
        <text class="greeting-text">你好，{{ nurseName || '护士' }}</text>
        <text class="date-text">{{ currentDate }}</text>
      </view>
      <view class="profile-avatar" @click="goToProfile">
        <image :src="avatarUrl" class="avatar-image"></image>
      </view>
    </view>

    <!-- Statistics Cards -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{ stats.total_orders }}</text>
          <text class="stat-label">总订单</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ stats.today_orders }}</text>
          <text class="stat-label">今日订单</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ stats.completed_orders }}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ stats.pending_orders }}</text>
          <text class="stat-label">待处理</text>
        </view>
      </view>
    </view>

    <!-- Today's Tasks -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">今日任务</text>
        <text class="section-more" @click="goToOrders">查看全部</text>
      </view>

      <view v-if="loadingTasks" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="todayTasks.length === 0" class="empty-tasks">
        <image src="/static/icon/order.png" class="empty-image"></image>
        <text class="empty-text">今日暂无任务</text>
      </view>

      <view v-else class="task-list">
        <view class="task-item" v-for="task in todayTasks" :key="task.id" @click="goToTaskDetail(task.id)">
          <view class="task-header">
            <text class="task-service">{{ task.service.name }}</text>
            <text class="task-status" :class="`status-${getStatusClass(task.status)}`">
              {{ getStatusLabel(task.status) }}
            </text>
          </view>
          <view class="task-info">
            <text class="task-patient">患者: {{ task.patient.name }}</text>
            <text class="task-time" v-if="task.service_time">{{ task.service_time }}</text>
          </view>
          <view class="task-role">
            <text v-if="isMainNurse(task)" class="role-badge main">主护</text>
            <text v-else class="role-badge secondary">副护</text>
          </view>
        </view>
      </view>
    </view>

    <!-- Quick Actions -->
    <view class="actions-section">
      <view class="section-header">
        <text class="section-title">快捷操作</text>
      </view>
      <view class="action-grid">
        <view class="action-item" @click="goToOrders">
          <image src="/static/icon/order.png" class="action-icon"></image>
          <text class="action-text">我的订单</text>
        </view>
        <view class="action-item" @click="goToProfile">
          <image src="/static/icon/profile.png" class="action-icon"></image>
          <text class="action-text">个人信息</text>
        </view>
        <view class="action-item" @click="viewSchedule">
          <image src="/static/icon/calendar.png" class="action-icon"></image>
          <text class="action-text">工作安排</text>
        </view>
      </view>
    </view>

    <!-- Recent Activity -->
    <view class="activity-section">
      <view class="section-header">
        <text class="section-title">最近活动</text>
      </view>

      <view v-if="loadingActivity" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="recentActivity.length === 0" class="empty-activity">
        <text class="empty-text">暂无活动记录</text>
      </view>

      <view v-else class="activity-list">
        <view class="activity-item" v-for="activity in recentActivity" :key="activity.id">
          <view class="activity-content">
            <text class="activity-text">{{ activity.description }}</text>
            <text class="activity-time">{{ formatTime(activity.created_at) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { API_BASE_URL } from '@/utils/config.js';

// State
const nurseName = ref('');
const avatarUrl = ref('/static/icon/profile.png');
const currentUser = ref(null);
const loadingTasks = ref(true);
const loadingActivity = ref(true);

const stats = ref({
  total_orders: 0,
  today_orders: 0,
  completed_orders: 0,
  pending_orders: 0
});

const todayTasks = ref([]);
const recentActivity = ref([]);

// Computed
const currentDate = computed(() => {
  const now = new Date();
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  };
  return now.toLocaleDateString('zh-CN', options);
});

// Lifecycle
onMounted(() => {
  loadUserInfo();
});

onShow(() => {
  loadUserInfo();
  fetchStats();
  fetchTodayTasks();
  fetchRecentActivity();
});

// Methods
const loadUserInfo = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo) {
      currentUser.value = userInfo;
      nurseName.value = userInfo.nickname || '护士';
      avatarUrl.value = userInfo.avatarUrl || '/static/icon/profile.png';
    }
  } catch (error) {
    console.error('Failed to load user info:', error);
  }
};

const fetchStats = async () => {
  const token = uni.getStorageSync('token');
  if (!token) return;

  try {
    uni.request({
      url: `${API_BASE_URL}/orders/nurse/stats`,
      method: 'GET',
      header: { 'Authorization': `Bearer ${token}` },
      success: (res) => {
        if (res.statusCode === 200) {
          stats.value = {
            total_orders: res.data.total_orders || 0,
            today_orders: res.data.today_orders || 0,
            completed_orders: res.data.completed_orders || 0,
            pending_orders: res.data.pending_orders || 0
          };
        }
      },
      fail: (error) => {
        console.error('Failed to fetch stats:', error);
      }
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
  }
};

const fetchTodayTasks = async () => {
  const token = uni.getStorageSync('token');
  if (!token) {
    loadingTasks.value = false;
    return;
  }

  loadingTasks.value = true;
  const today = new Date().toISOString().split('T')[0];

  try {
    uni.request({
      url: `${API_BASE_URL}/orders/nurse/assigned?limit=10&date_from=${today}&date_to=${today}`,
      method: 'GET',
      header: { 'Authorization': `Bearer ${token}` },
      success: (res) => {
        if (res.statusCode === 200) {
          todayTasks.value = res.data.orders || [];
        }
      },
      fail: (error) => {
        console.error('Failed to fetch today tasks:', error);
      },
      complete: () => {
        loadingTasks.value = false;
      }
    });
  } catch (error) {
    console.error('Error fetching today tasks:', error);
    loadingTasks.value = false;
  }
};

const fetchRecentActivity = async () => {
  // Mock recent activity for now
  loadingActivity.value = true;

  setTimeout(() => {
    recentActivity.value = [
      {
        id: 1,
        description: '完成了张三的护理服务',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        description: '接受了李四的护理订单',
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        description: '更新了个人资料',
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];
    loadingActivity.value = false;
  }, 1000);
};

const isMainNurse = (task) => {
  return currentUser.value && task.primary_nurse && task.primary_nurse.user_id === currentUser.value.id;
};

const getStatusClass = (status) => {
  const statusMap = {
    'UNPAID': 'unpaid',
    'PAID': 'paid',
    'PROCESSING': 'processing',
    'COMPLETED': 'completed',
    'CANCELED': 'canceled',
    'REFUNDING': 'refunding',
    'REFUNDED': 'refunded',
    'FAILED': 'failed',
    'EXPIRED': 'expired'
  };
  return statusMap[status] || 'unknown';
};

const getStatusLabel = (status) => {
  const statusMap = {
    'UNPAID': '待支付',
    'PAID': '待服务',
    'PROCESSING': '服务中',
    'COMPLETED': '已完成',
    'CANCELED': '已取消',
    'REFUNDING': '退款中',
    'REFUNDED': '已退款',
    'FAILED': '支付失败',
    'EXPIRED': '已过期'
  };
  return statusMap[status] || '未知状态';
};

const formatTime = (timeString) => {
  if (!timeString) return '';
  const now = new Date();
  const time = new Date(timeString);
  const diffMs = now - time;
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);

  if (diffHours < 1) {
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return time.toLocaleDateString('zh-CN');
  }
};

// Navigation methods
const goToProfile = () => {
  uni.switchTab({
    url: '/pages/profile/profile'
  });
};

const goToOrders = () => {
  uni.switchTab({
    url: '/pages/order/order'
  });
};

const goToTaskDetail = (taskId) => {
  // TODO: Navigate to task detail
  uni.showToast({ title: '任务详情功能开发中', icon: 'none' });
};

const contactSupport = () => {
  uni.showToast({
    title: '客服功能开发中',
    icon: 'none'
  });
};

const viewSchedule = () => {
  uni.showToast({
    title: '工作安排功能开发中',
    icon: 'none'
  });
};
</script>

<style scoped>
.home-container {
  background-color: #f4f4f4;
  min-height: 100vh;
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 15px;
  color: white;
}

.greeting {
  flex: 1;
}

.greeting-text {
  font-size: 20px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.date-text {
  font-size: 14px;
  opacity: 0.9;
}

.profile-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

/* Statistics Section */
.stats-section {
  background-color: white;
  margin: 10px 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 14px;
  color: #667eea;
}

.section-more::after {
  content: ">";
  margin-left: 4px;
}


/* Tasks Section */
.tasks-section {
  background-color: white;
  margin: 10px 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-container, .empty-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
}

.loading-text, .empty-text {
  font-size: 14px;
  margin-top: 10px;
}

.empty-image {
  width: 60px;
  height: 60px;
  opacity: 0.5;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #667eea;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-service {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
}

.status-paid { background-color: #52c41a; }
.status-processing { background-color: #1890ff; }
.status-completed { background-color: #52c41a; }

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-patient {
  font-size: 14px;
  color: #666;
}

.task-time {
  font-size: 14px;
  color: #667eea;
  font-weight: bold;
}

.task-role {
  display: flex;
  justify-content: flex-end;
}

.role-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  color: white;
}

.role-badge.main {
  background-color: #667eea;
}

.role-badge.secondary {
  background-color: #52c41a;
}

/* Actions Section */
.actions-section {
  background-color: white;
  margin: 10px 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-grid {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 22%;
  padding: 15px 5px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.action-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.action-text {
  font-size: 12px;
  color: #333;
  text-align: center;
}

/* Activity Section */
.activity-section {
  background-color: white;
  margin: 10px 15px 20px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-activity {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #999;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-text {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.activity-time {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}
</style>
