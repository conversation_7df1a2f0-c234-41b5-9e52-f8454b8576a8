<template>
  <view class="form-container">
    <view class="form-item">
      <text class="label">姓名 *</text>
      <input v-model="formData.name" placeholder="请输入真实姓名" class="input" type="text" />
      <text v-if="errors.name" class="error">{{ errors.name }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">性别 *</text>
      <picker @change="onGenderChange" :value="genderIndex" :range="genderOptions" class="picker-wrapper">
        <view class="picker">{{ genderOptions[genderIndex] }}</view>
      </picker>
      <text v-if="errors.gender" class="error">{{ errors.gender }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">职称 *</text>
      <input v-model="formData.title" placeholder="如：主管护师" class="input" />
      <text v-if="errors.title" class="error">{{ errors.title }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">科室 *</text>
      <input v-model="formData.department" placeholder="请输入科室" class="input" />
      <text v-if="errors.department" class="error">{{ errors.department }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">执业证书号 *</text>
      <input v-model="formData.license_number" placeholder="请输入执业证书号" class="input" />
      <text v-if="errors.license_number" class="error">{{ errors.license_number }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">工作年限</text>
      <input v-model="formData.years_of_experience" placeholder="请输入工作年限" class="input" type="number" />
      <text v-if="errors.years_of_experience" class="error">{{ errors.years_of_experience }}</text>
    </view>
    
    <view class="form-item">
      <text class="label">专业特长</text>
      <textarea v-model="formData.specialties" placeholder="请输入专业特长（可选）" class="textarea" />
      <text v-if="errors.specialties" class="error">{{ errors.specialties }}</text>
    </view>

    <view class="form-item">
      <text class="label">执业证书文件 *</text>
      <text class="label-desc">请上传执业证书照片或PDF文件</text>

      <!-- Document Upload Section -->
      <view class="document-upload-section">
        <!-- Upload Button -->
        <view class="upload-button" @click="chooseDocument" v-if="uploadedDocuments.length < 3">
          <text class="upload-icon">📄</text>
          <text class="upload-text">{{ uploadingDocument ? '上传中...' : '选择文件' }}</text>
        </view>

        <!-- Uploaded Documents List -->
        <view class="document-list" v-if="uploadedDocuments.length > 0">
          <view class="document-item" v-for="(doc, index) in uploadedDocuments" :key="index">
            <view class="document-info">
              <text class="document-name">{{ doc.name }}</text>
              <text class="document-size">{{ formatFileSize(doc.size) }}</text>
            </view>
            <view class="document-actions">
              <text class="action-btn preview-btn" @click="previewDocument(doc)">预览</text>
              <text class="action-btn delete-btn" @click="removeDocument(index)">删除</text>
            </view>
          </view>
        </view>

        <!-- Upload Progress -->
        <view class="upload-progress" v-if="uploadingDocument">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ uploadProgress }}%</text>
        </view>
      </view>

      <text v-if="errors.documents" class="error">{{ errors.documents }}</text>
      <text class="help-text">支持PDF、JPG、PNG格式，单个文件不超过10MB，最多上传3个文件</text>
    </view>

    <button type="primary" @click="submit" :loading="isSubmitting">
      {{ isSubmitting ? '提交中...' : (isEditMode ? '保存修改' : '完成注册') }}
    </button>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';

const isEditMode = ref(false);
const isSubmitting = ref(false);
const genderIndex = ref(0);
const genderOptions = ['男', '女'];

const formData = reactive({
  name: '',
  gender: '男',
  title: '',
  department: '',
  license_number: '',
  years_of_experience: 0,
  specialties: ''
});

// Document upload related variables
const uploadedDocuments = ref([]);
const uploadingDocument = ref(false);
const uploadProgress = ref(0);

const errors = reactive({});

onLoad(async (options) => {
  // Check if we're editing existing profile
  await checkAndLoadNurseProfile();
});

const checkAndLoadNurseProfile = async () => {
  try {
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.setNavigationBarTitle({
        title: '完善护士信息'
      });
      return;
    }

    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/nurses/me`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        success: resolve,
        fail: reject
      });
    });

    if (response.statusCode === 200 && response.data) {
      // Profile exists, populate form with existing data
      const nurseData = response.data;
      isEditMode.value = true;
      
      Object.assign(formData, {
        name: nurseData.name || '',
        gender: nurseData.gender || '男',
        title: nurseData.title || '',
        department: nurseData.department || '',
        license_number: nurseData.license_number || '',
        years_of_experience: nurseData.years_of_experience || 0,
        specialties: nurseData.specialties || ''
      });
      
      // Set gender picker index
      genderIndex.value = nurseData.gender === '女' ? 1 : 0;

      // Load existing documents
      if (nurseData.documents && nurseData.documents.length > 0) {
        uploadedDocuments.value = nurseData.documents.map(url => ({
          name: url.split('/').pop(), // Extract filename from URL
          size: 0, // Size not available from server
          url: url,
          path: url
        }));
      }

      uni.setNavigationBarTitle({
        title: '编辑护士信息'
      });
    } else {
      // No profile found
      isEditMode.value = false;
      uni.setNavigationBarTitle({
        title: '完善护士信息'
      });
    }
  } catch (error) {
    console.error('Error loading nurse profile:', error);
    isEditMode.value = false;
    uni.setNavigationBarTitle({
      title: '完善护士信息'
    });
  }
};

const onGenderChange = (e) => {
  genderIndex.value = e.detail.value;
  formData.gender = genderIndex.value === 0 ? '男' : '女';
};

// Document upload functions
const chooseDocument = () => {
  uni.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['.pdf', '.jpg', '.jpeg', '.png'],
    success: (res) => {
      const file = res.tempFiles[0];

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过10MB',
          icon: 'none'
        });
        return;
      }

      // Validate file type
      const allowedTypes = ['pdf', 'jpg', 'jpeg', 'png'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        uni.showToast({
          title: '只支持PDF、JPG、PNG格式',
          icon: 'none'
        });
        return;
      }

      uploadDocument(file);
    },
    fail: (err) => {
      console.error('Choose file failed:', err);
      uni.showToast({
        title: '选择文件失败',
        icon: 'none'
      });
    }
  });
};

const uploadDocument = async (file) => {
  uploadingDocument.value = true;
  uploadProgress.value = 0;

  try {
    const token = uni.getStorageSync('token');

    const uploadTask = uni.uploadFile({
      url: `${API_BASE_URL}/nurses/upload-document`,
      filePath: file.path,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (uploadRes) => {
        if (uploadRes.statusCode === 200) {
          const response = JSON.parse(uploadRes.data);

          // Add to uploaded documents list
          uploadedDocuments.value.push({
            name: file.name,
            size: file.size,
            url: response.url,
            path: file.path
          });

          // Add to nurse's document list via API
          addDocumentToProfile(response.url);

          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          throw new Error('Upload failed');
        }
      },
      fail: (err) => {
        console.error('Upload failed:', err);
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        uploadingDocument.value = false;
        uploadProgress.value = 0;
      }
    });

    // Monitor upload progress
    uploadTask.onProgressUpdate((res) => {
      uploadProgress.value = res.progress;
    });

  } catch (error) {
    console.error('Upload error:', error);
    uploadingDocument.value = false;
    uploadProgress.value = 0;
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    });
  }
};

const addDocumentToProfile = async (documentUrl) => {
  try {
    const token = uni.getStorageSync('token');

    await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/nurses/me/documents`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          document_url: documentUrl
        },
        success: resolve,
        fail: reject
      });
    });
  } catch (error) {
    console.error('Failed to add document to profile:', error);
  }
};

const removeDocument = async (index) => {
  const document = uploadedDocuments.value[index];

  try {
    const token = uni.getStorageSync('token');

    // Remove from server
    await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/nurses/me/documents`,
        method: 'DELETE',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          document_url: document.url
        },
        success: resolve,
        fail: reject
      });
    });

    // Remove from local list
    uploadedDocuments.value.splice(index, 1);

    uni.showToast({
      title: '删除成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('Failed to remove document:', error);
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    });
  }
};

const previewDocument = (document) => {
  const previewUrl = `${STATIC_BASE_URL}${document.url}`;
  console.log(previewUrl);
  // For images, use previewImage
  if (document.name.match(/\.(jpg|jpeg|png|gif)$/i)) {
    uni.previewImage({
      urls: [previewUrl],
      current: previewUrl
    });
  } else {
    // For non-image files like PDF, download and open
    uni.showLoading({
      title: '正在打开文件...'
    });
    uni.downloadFile({
      url: previewUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            showMenu: true, // Show a menu for sharing, etc.
            fail: (openErr) => {
              console.error('Open document failed:', openErr);
              uni.showToast({
                title: '打开文件失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({ title: '文件下载失败', icon: 'none' });
        }
      },
      fail: (downloadErr) => {
        console.error('Download file failed:', downloadErr);
        uni.showToast({ title: '文件下载失败', icon: 'none' });
      },
      complete: () => {
        uni.hideLoading();
      }
    });
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const validateField = (field, value) => {
  errors[field] = '';
  if (field === 'name' || field === 'title' || field === 'department' || field === 'license_number') {
    if (!value || value.trim() === '') {
      errors[field] = '此项不能为空';
      return false;
    }
  }
  if (field === 'years_of_experience' && value < 0) {
    errors[field] = '工作年限不能为负数';
    return false;
  }
  return true;
};

const submit = async () => {
  // Clear previous errors
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });

  // Validate required fields
  const requiredFields = ['name', 'title', 'department', 'license_number'];
  let hasErrors = false;
  
  requiredFields.forEach(field => {
    if (!validateField(field, formData[field])) {
      hasErrors = true;
    }
  });

  // Validate years_of_experience
  if (!validateField('years_of_experience', formData.years_of_experience)) {
    hasErrors = true;
  }

  // Validate documents (required for new registrations)
  if (!isEditMode.value && uploadedDocuments.value.length === 0) {
    errors.documents = '请上传至少一个执业证书文件';
    hasErrors = true;
  }

  if (hasErrors) {
    uni.showToast({
      title: '请填写必填项并上传证书文件',
      icon: 'none'
    });
    return;
  }

  try {
    isSubmitting.value = true;
    const token = uni.getStorageSync('token');
    
    if (isEditMode.value) {
      // Update existing profile
      const updateData = {
        name: formData.name,
        gender: formData.gender,
        title: formData.title,
        department: formData.department,
        license_number: formData.license_number,
        years_of_experience: parseInt(formData.years_of_experience) || 0,
        specialties: formData.specialties || ''
      };

      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/nurses/me`,
          method: 'PATCH',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          data: updateData,
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200) {
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      } else {
        throw new Error(getErrorMessage(response, '更新失败'));
      }
    } else {
      // Create new profile
      const userInfo = uni.getStorageSync('userInfo') || {};
      
      const submitData = {
        name: formData.name,
        gender: formData.gender,
        title: formData.title,
        department: formData.department,
        license_number: formData.license_number,
        years_of_experience: parseInt(formData.years_of_experience) || 0,
        specialties: formData.specialties || '',
        is_active: false,
        nickname: userInfo.nickname || '',
        avatar: userInfo.avatarUrl || '',
        phone: userInfo.phone || ''
      };

      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/nurses/me`,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          data: submitData,
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200) {
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        });
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1000);
      } else {
        throw new Error(getErrorMessage(response, '注册失败'));
      }
    }

  } catch (error) {
    console.error('Profile submission failed:', error);
    let displayMessage = isEditMode.value ? '更新失败，请重试' : '注册失败，请重试';

    if (error.message && error.message !== '[object Object]') {
      displayMessage = error.message;
    }

    uni.showToast({
      title: displayMessage,
      icon: 'none',
      duration: 3000
    });
  } finally {
    isSubmitting.value = false;
  }
};

// Helper function to extract error messages
const getErrorMessage = (response, defaultMessage) => {
  if (response.data) {
    if (response.data.detail) {
      return response.data.detail;
    } else if (response.data.message) {
      return response.data.message;
    } else if (typeof response.data === 'string') {
      return response.data;
    }
  }
  return defaultMessage;
};
</script>

<style scoped>
.form-container {
  padding: 15px;
  background-color: #fff;
  min-height: 100vh;
}

.form-item {
  margin-bottom: 15px;
}

.label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.input, .textarea {
  width: 100%;
  padding: 0 20rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.input {
  height: 60rpx;
}

.textarea {
  height: 120rpx;
  padding: 20rpx;
  align-items: flex-start;
}

.picker-wrapper {
  width: 100%;
}

.picker {
  width: 100%;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 5px;
}

.label-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.help-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.document-upload-section {
  margin-top: 20rpx;
}

.upload-button {
  width: 100%;
  height: 120rpx;
  background-color: #f7f7f7;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

.document-list {
  margin-top: 20rpx;
}

.document-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.document-info {
  flex: 1;
}

.document-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.document-size {
  font-size: 24rpx;
  color: #999;
}

.document-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 4rpx;
  text-align: center;
}

.preview-btn {
  background-color: #007aff;
  color: white;
}

.delete-btn {
  background-color: #ff3b30;
  color: white;
}

.upload-progress {
  margin-top: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007aff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 10rpx;
}
</style>
