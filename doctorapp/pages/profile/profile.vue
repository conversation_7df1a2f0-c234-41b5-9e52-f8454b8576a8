<template>
  <view class="nurse-container">
    <view class="profile-header">
      <!-- Avatar selection button -->
      <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
        <image class="avatar" :src="avatarUrl"></image>
      </button>

      <!-- Nickname input -->
      <input
        type="nickname"
        class="weui-input nickname-input"
        placeholder="请输入昵称"
        v-model="nickname"
        @blur="saveNickname"
        @confirm="saveNickname"
      />
    </view>

    <view class="menu-list">
      <view class="menu-item" @click="gotoNurseInfo">
        <view>护士信息</view>
        <view class="menu-item-right">
          <text class="status-text">{{ nurseStatusText }}</text>
          <view class="arrow" v-html="'>'"></view>
        </view>
      </view>
      <view class="menu-item" @click="contactSupport">
        <view>联系客服</view>
        <view class="arrow" v-html="'>'"></view>
      </view>
    </view>

    <LogoutButton />
  </view>
</template>

<script setup>
import LogoutButton from '@/components/LogoutButton.vue';
import { ref, onMounted, computed } from 'vue';
import { API_BASE_URL } from '@/utils/config.js';

// Profile data
const avatarUrl = ref('/static/icon/profile.png');
const nickname = ref('');

// Nurse status tracking
const isEditMode = ref(false)

// Computed property for nurse status text
const nurseStatusText = computed(() => {
  if (isEditMode.value) {
    return '已完善'
  } else {
    return '待完善'
  }
})

// Load saved data on component mount
onMounted(() => {
  loadUserProfile();
  checkNurseProfile();
});

// Load user profile from storage
const loadUserProfile = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo) {
      avatarUrl.value = userInfo.avatarUrl || '/static/icon/profile.png';
      nickname.value = userInfo.nickname || '';
    }
  } catch (error) {
    console.error('Failed to load user profile:', error);
  }
};

// Update user profile on the server
const updateUserProfileOnServer = (data) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.showToast({ title: '请先登录', icon: 'none' });
      return reject('Not logged in');
    }

    uni.request({
      url: `${API_BASE_URL}/users/me`,
      method: 'PATCH',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: data,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
};

// Handle avatar selection
const onChooseAvatar = async (res) => {
  if (res.detail.avatarUrl) {
    const newAvatarUrl = res.detail.avatarUrl;
    try {
      await updateUserProfileOnServer({ avatar: newAvatarUrl });

      // Update local state and storage
      avatarUrl.value = newAvatarUrl;
      const userInfo = uni.getStorageSync('userInfo') || {};
      userInfo.avatarUrl = newAvatarUrl;
      uni.setStorageSync('userInfo', userInfo);
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('Failed to update avatar:', error);
      uni.showToast({
        title: '头像更新失败',
        icon: 'none'
      });
    }
  }
};

// Save nickname
const saveNickname = async () => {
  const trimmedNickname = nickname.value.trim();
  if (trimmedNickname) {
    try {
      await updateUserProfileOnServer({ nickname: trimmedNickname });

      // Update local state and storage
      const userInfo = uni.getStorageSync('userInfo') || {};
      userInfo.nickname = trimmedNickname;
      uni.setStorageSync('userInfo', userInfo);

      uni.showToast({
        title: '昵称保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('Failed to save nickname:', error);
      uni.showToast({
        title: '昵称保存失败',
        icon: 'none'
      });
    }
  }
};

// Navigation functions
const gotoNurseInfo = () => {
  uni.navigateTo({
    url: '/pages/nurse/form'
  });
};

const contactSupport = () => {
  uni.showToast({
    title: '功能暂未开放',
    icon: 'none'
  });
};

// Check if nurse profile exists (for status display)
const checkNurseProfile = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return

    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/nurses/me`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        success: resolve,
        fail: reject
      })
    })

    if (response.statusCode === 200 && response.data) {
      isEditMode.value = true
    } else {
      isEditMode.value = false
    }
  } catch (error) {
    isEditMode.value = false
  }
};






</script>

<style lang="scss" scoped>
.nurse-container {
  background-color: #f4f4f4;
  min-height: 100vh;
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  background-color: #fff;
}

.avatar-wrapper {
  background: none;
  border: none;
  padding: 0;
  margin-bottom: 15px;
  border-radius: 50%;
  overflow: hidden;
  width: 80px;
  height: 80px;
  position: relative;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

.nickname-input {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 8px 16px;
  margin-top: 10px;
  width: 200px;
}

.menu-list {
  margin-top: 10px;
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f8f8f8;
}

.menu-item-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 14px;
  color: #999;
}

.arrow {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}


</style>
