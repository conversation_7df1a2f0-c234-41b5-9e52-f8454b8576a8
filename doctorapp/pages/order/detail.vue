<template>
  <view class="order-detail-container">
    <view v-if="loading" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view v-else-if="order" class="detail-content">
      <!-- Order Status Card -->
      <view class="status-card">
        <view class="status-icon" :class="`status-${getStatusClass(order.status)}`">
          <text class="status-text">{{ getStatusIcon(order.status) }}</text>
        </view>
        <view class="status-info">
          <text class="status-title">{{ getStatusLabel(order.status) }}</text>
          <text class="status-desc">{{ getStatusDescription(order.status) }}</text>
        </view>
      </view>

      <!-- Order Basic Info -->
      <view class="info-card">
        <view class="card-title">订单信息</view>
        <view class="info-row">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ order.order_num }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">服务项目</text>
          <text class="info-value">{{ order.service.name }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">服务金额</text>
          <text class="info-value amount">￥{{ parseFloat(order.amount).toFixed(2) }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ formatDateTime(order.created_at) }}</text>
        </view>
      </view>

      <!-- Service Schedule -->
      <view class="info-card">
        <view class="card-title">服务安排</view>
        <view class="info-row">
          <text class="info-label">服务日期</text>
          <text class="info-value">{{ formatDate(order.service_date) }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">服务时间</text>
          <text class="info-value">{{ order.service_time }}</text>
        </view>
        <view class="info-row" v-if="order.primary_nurse">
          <text class="info-label">主护士</text>
          <text class="info-value">{{ order.primary_nurse.name }} ({{ order.primary_nurse.department }})</text>
        </view>
        <view class="info-row" v-if="order.secondary_nurse">
          <text class="info-label">副护士</text>
          <text class="info-value">{{ order.secondary_nurse.name }} ({{ order.secondary_nurse.department }})</text>
        </view>
      </view>

      <!-- Patient Info -->
      <view class="info-card">
        <view class="card-title">患者信息</view>
        <view class="info-row">
          <text class="info-label">患者姓名</text>
          <text class="info-value">{{ order.patient.name }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ order.patient.phone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">服务地址</text>
          <text class="info-value">{{ getPatientAddress(order.patient) }}</text>
        </view>
      </view>

      <!-- Service Notes -->
      <view class="info-card" v-if="order.notes">
        <view class="card-title">服务备注</view>
        <text class="notes-text">{{ order.notes }}</text>
      </view>

      <!-- Action Buttons -->
      <view class="action-section" v-if="showActions">
        <button 
          v-if="canStartService" 
          class="action-btn start-btn"
          @click="startService"
          :loading="isStarting"
        >
          {{ isStarting ? '处理中...' : '开始服务' }}
        </button>
        
        <button 
          v-if="canCompleteService" 
          class="action-btn complete-btn"
          @click="completeService"
          :loading="isCompleting"
        >
          {{ isCompleting ? '处理中...' : '完成服务' }}
        </button>
        
        <button 
          v-if="canContactPatient" 
          class="action-btn contact-btn"
          @click="contactPatient"
        >
          联系患者
        </button>
      </view>
    </view>
    
    <view v-else class="error-state">
      <text class="error-text">订单不存在或已被删除</text>
      <button class="back-button" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { API_BASE_URL } from '@/utils/config.js';

const orderId = ref(null);
const order = ref(null);
const loading = ref(true);
const isStarting = ref(false);
const isCompleting = ref(false);

onLoad((options) => {
  if (options.id) {
    orderId.value = parseInt(options.id);
    fetchOrderDetail();
  } else {
    loading.value = false;
  }
});

// Computed properties for order actions
const showActions = computed(() => {
  return order.value && ['PAID', 'PROCESSING'].includes(order.value.status);
});

const canStartService = computed(() => {
  return order.value && order.value.status === 'PAID';
});

const canCompleteService = computed(() => {
  return order.value && order.value.status === 'PROCESSING';
});

const canContactPatient = computed(() => {
  return order.value && ['PAID', 'PROCESSING'].includes(order.value.status);
});

// Status helper functions
const getStatusClass = (status) => {
  const statusMap = {
    'UNPAID': 'unpaid',
    'PAID': 'paid',
    'PROCESSING': 'processing',
    'COMPLETED': 'completed',
    'CANCELLED': 'cancelled',
    'EXPIRED': 'expired',
    'FAILED': 'failed'
  };
  return statusMap[status] || 'unknown';
};

const getStatusLabel = (status) => {
  const statusMap = {
    'UNPAID': '待支付',
    'PAID': '已支付',
    'PROCESSING': '服务中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'EXPIRED': '已过期',
    'FAILED': '支付失败'
  };
  return statusMap[status] || '未知状态';
};

const getStatusIcon = (status) => {
  const iconMap = {
    'UNPAID': '💰',
    'PAID': '✅',
    'PROCESSING': '🔄',
    'COMPLETED': '🎉',
    'CANCELLED': '❌',
    'EXPIRED': '⏰',
    'FAILED': '⚠️'
  };
  return iconMap[status] || '❓';
};

const getStatusDescription = (status) => {
  const descMap = {
    'UNPAID': '等待患者支付',
    'PAID': '已支付，等待服务',
    'PROCESSING': '正在提供服务',
    'COMPLETED': '服务已完成',
    'CANCELLED': '订单已取消',
    'EXPIRED': '订单已过期',
    'FAILED': '支付失败'
  };
  return descMap[status] || '状态未知';
};

// Date formatting functions
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// Helper function to get patient's full address
const getPatientAddress = (patient) => {
  if (!patient) return '';

  // Handle both old format (address) and new format (base_address + detail_address)
  if (patient.address) {
    return patient.address; // Backward compatibility
  }

  const baseAddr = patient.base_address || '';
  const detailAddr = patient.detail_address || '';

  if (baseAddr && detailAddr) {
    return `${baseAddr} ${detailAddr}`;
  } else if (baseAddr) {
    return baseAddr;
  } else if (detailAddr) {
    return detailAddr;
  }

  return '地址未设置';
};

// API functions
const fetchOrderDetail = async () => {
  const token = uni.getStorageSync('token');
  if (!token) {
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }

  loading.value = true;
  
  uni.request({
    url: `${API_BASE_URL}/orders/${orderId.value}`,
    method: 'GET',
    header: {
      'Authorization': `Bearer ${token}`
    },
    success: (res) => {
      if (res.statusCode === 200) {
        order.value = res.data;
      } else {
        uni.showToast({ title: '获取订单详情失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    },
    complete: () => {
      loading.value = false;
    }
  });
};

// Action functions
const startService = async () => {
  uni.showModal({
    title: '开始服务',
    content: '确认开始为患者提供服务？',
    success: async (res) => {
      if (res.confirm) {
        isStarting.value = true;
        const token = uni.getStorageSync('token');
        
        uni.request({
          url: `${API_BASE_URL}/orders/${orderId.value}/start`,
          method: 'PATCH',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              uni.showToast({ title: '服务已开始', icon: 'success' });
              fetchOrderDetail(); // Refresh order data
            } else {
              uni.showToast({ title: '操作失败', icon: 'none' });
            }
          },
          fail: () => {
            uni.showToast({ title: '网络错误', icon: 'none' });
          },
          complete: () => {
            isStarting.value = false;
          }
        });
      }
    }
  });
};

const completeService = async () => {
  uni.showModal({
    title: '完成服务',
    content: '确认已完成服务？',
    success: async (res) => {
      if (res.confirm) {
        isCompleting.value = true;
        const token = uni.getStorageSync('token');
        
        uni.request({
          url: `${API_BASE_URL}/orders/${orderId.value}/complete`,
          method: 'PATCH',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              uni.showToast({ title: '服务已完成', icon: 'success' });
              fetchOrderDetail(); // Refresh order data
            } else {
              uni.showToast({ title: '操作失败', icon: 'none' });
            }
          },
          fail: () => {
            uni.showToast({ title: '网络错误', icon: 'none' });
          },
          complete: () => {
            isCompleting.value = false;
          }
        });
      }
    }
  });
};

const contactPatient = () => {
  if (order.value && order.value.patient.phone) {
    uni.makePhoneCall({
      phoneNumber: order.value.patient.phone,
      fail: () => {
        uni.showToast({ title: '拨打电话失败', icon: 'none' });
      }
    });
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style scoped>
.order-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.detail-content {
  padding-bottom: 40rpx;
}

/* Status Card */
.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.status-text {
  font-size: 32rpx;
}

.status-paid { background: #e7f5e7; }
.status-processing { background: #e3f2fd; }
.status-completed { background: #f3e5f5; }
.status-unpaid { background: #fff3e0; }
.status-cancelled { background: #ffebee; }
.status-expired { background: #f5f5f5; }
.status-failed { background: #ffebee; }

.status-info {
  flex: 1;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 24rpx;
  color: #666;
}

/* Info Cards */
.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eee;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value.amount {
  color: #ff6b35;
  font-weight: bold;
  font-size: 32rpx;
}

.notes-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}

/* Action Section */
.action-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  border: none;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.start-btn {
  background: #4CAF50;
  color: white;
}

.complete-btn {
  background: #2196F3;
  color: white;
}

.contact-btn {
  background: #FF9800;
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background: white;
  border-radius: 16rpx;
  margin: 40rpx 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.back-button {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
