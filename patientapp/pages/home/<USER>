<template>
  <view class="home-container">
    <!-- Search Bar -->
    <!-- <view class="search-bar">
      <uni-search-bar @confirm="search" placeholder="搜索服务或护理人员"></uni-search-bar>
    </view> -->

    <!-- Banner -->
    <swiper v-if="bannerItems.length > 0" class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
      <swiper-item v-for="(item, index) in bannerItems" :key="index">
        <image :src="item.imageUrl" class="banner-image" mode="aspectFit"></image>
      </swiper-item>
    </swiper>

    <!-- Service Categories -->
    <!-- <view class="category-grid">
      <view class="grid-item" v-for="category in categories" :key="category.id" @click="goToCategory(category)">
        <image :src="category.iconUrl" class="grid-icon"></image>
        <text class="grid-text">{{ category.name }}</text>
      </view>
    </view> -->

    <!-- Recommended Services -->
    <view class="recommended-section">
      <view class="section-header">
        <view class="section-title">热门服务</view>
        <view class="section-more" @click="seeMoreServices" v-html="'查看更多 >'"></view>
      </view>
      <view class="service-list">
        <!-- Loading Skeleton -->
        <template v-if="loading">
           <view class="skeleton-item" v-for="i in 3" :key="i"></view>
        </template>
        <!-- Service List -->
        <template v-else>
          <view class="service-item" v-for="service in recommendedServices" :key="service.id" @click="goToServiceDetail(service.id)">
            <image :src="service.imageUrl" class="service-image" mode="aspectFill"></image>
            <view class="service-info">
              <text class="service-name">{{ service.name }}</text>
              <text class="service-description">{{ service.description }}</text>
              <view class="service-footer">
                <text class="service-price">￥{{ service.price }}/次</text>
                <button class="order-button" size="mini">立即预约</button>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';

// --- State ---

const bannerItems = ref([]);
const categories = ref([]);
const recommendedServices = ref([]);
const loading = ref(true);

// --- Lifecycle Hooks ---

onMounted(() => {
  fetchHomePageData();
});

// --- Methods ---

const fetchHomePageData = () => {
  loading.value = true;
  uni.request({
    url: `${API_BASE_URL}/services/home`, // A single endpoint for all home page data
    method: 'GET',
    success: (res) => {
      if (res.statusCode === 200 && res.data) {
        const data = res.data;
        // Process banners by prepending the server URL
        bannerItems.value = data.banners.map(item => ({
          ...item,
          imageUrl: `${STATIC_BASE_URL}${item.image_url}`
        }));
        // Process categories
        // categories.value = data.categories.map(item => ({
        //   ...item,
        //   iconUrl: `${STATIC_BASE_URL}${item.icon_url}`
        // }));
        // Process recommended services
        recommendedServices.value = data.recommended_services.map(item => ({
          ...item,
          imageUrl: `${STATIC_BASE_URL}${item.image_url}`
        }));
      } else {
        console.error('Failed to fetch home page data:', res);
      }
    },
    fail: (err) => {
      console.error('API request failed:', err);
      uni.showToast({ title: '网络错误，请稍后再试', icon: 'none' });
    },
    complete: () => {
      loading.value = false;
    }
  });
};

const search = (e) => {
  console.log('Searching for:', e.value);
  uni.navigateTo({
    url: `/pages/search/results?keyword=${e.value}`
  });
};

const goToCategory = (category) => {
  console.log('Go to category:', category.name);
  uni.switchTab({
    url: '/pages/service/service'
  });
};

const seeMoreServices = () => {
  console.log('See more services');
  uni.switchTab({
    url: '/pages/service/service'
  });
};

const goToServiceDetail = (serviceId) => {
  console.log('Go to service detail:', serviceId);
  uni.navigateTo({
    url: `/pages/order/ordering?id=${serviceId}`
  });
};
</script>

<style scoped>
.skeleton-item {
  height: 100px;
  background-color: #e9e9e9;
  border-radius: 5px;
  margin-bottom: 15px;
  animation: skeleton-blink 1.2s infinite ease-in-out;
}

.skeleton-item:last-child {
  margin-bottom: 0;
}

@keyframes skeleton-blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.home-container {
  display: flex;
  flex-direction: column;
  background-color: #f4f4f4;
}

/* Search Bar */
.search-bar {
  background-color: #ffffff;
  padding: 8px 10px;
}

/* Banner */
.banner-swiper {
  width: 100%;
  height: 180px;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* Category Grid */
.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  background-color: #ffffff;
  padding: 15px 0;
  margin-top: 10px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.3%; /* Adjusted for 3 items */
  margin-bottom: 10px;
}

.grid-icon {
  width: 45px;
  height: 45px;
  margin-bottom: 5px;
}

.grid-text {
  font-size: 14px;
  color: #333;
}

/* Recommended Services */
.recommended-section {
  margin-top: 10px;
  background-color: #ffffff;
  padding: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 14px;
  color: #999;
}

.service-list {
  display: flex;
  flex-direction: column;
}

.service-item {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.service-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.service-image {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  margin-right: 15px;
  flex-shrink: 0;
}

.service-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}

.service-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.service-description {
  font-size: 13px;
  color: #666;
  margin-top: 5px;
  /* Ellipsis for long text */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.service-price {
  font-size: 16px;
  color: #ff5050;
  font-weight: bold;
}

.order-button {
  background-color: #007aff;
  color: white;
  border-radius: 20px;
  padding: 0 12px;
  line-height: 28px;
  height: 28px;
  font-size: 13px;
}
</style>