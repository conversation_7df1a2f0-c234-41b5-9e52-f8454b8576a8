<template>
  <view class="form-container">
    <view class="form-item">
      <text class="label">姓名:</text>
      <input v-model="formData.name" placeholder="请输入患者姓名" class="input" type="text" />
      <text v-if="errors.name" class="error">{{ errors.name }}</text>
    </view>
    <view class="form-item">
      <text class="label">身份证号:</text>
      <input v-model="formData.id_num" placeholder="请输入身份证号" class="input" />
      <text v-if="errors.id_num" class="error">{{ errors.id_num }}</text>
    </view>
    <view class="form-item">
      <text class="label">手机号:</text>
      <input v-model="formData.phone" placeholder="请输入手机号" class="input" />
      <text v-if="errors.phone" class="error">{{ errors.phone }}</text>
    </view>
    
    <!-- 地址选择区域 -->
    <view class="form-item">
      <text class="label">地理位置:</text>
      <view class="location-selector" @click="chooseLocation">
        <text class="location-text">{{ formData.base_address || '点击选择省市区街道' }}</text>
        <!-- <text class="location-tip">从地图选择位置</text> -->
      </view>
      <text v-if="errors.base_address" class="error">{{ errors.base_address }}</text>
    </view>

    <view class="form-item">
      <text class="label">详细地址:</text>
      <input 
        v-model="formData.detail_address" 
        placeholder="请输入楼层、门牌号等详细信息" 
        class="input" 
        type="text"
      />
      <!-- <text class="input-tip">如: A栋1单元202室</text> -->
      <text v-if="errors.detail_address" class="error">{{ errors.detail_address }}</text>
    </view>
    
    <button type="primary" @click="submit">提交</button>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive
  } from 'vue';
  import {
    onLoad
  } from '@dcloudio/uni-app';
  import {
    API_BASE_URL
  } from '@/utils/config.js';

  const RANGE_CONFIG = {
    center: {
      latitude: 34.259169,
      longitude: 108.917916
    },
    // 允许的最大半径 (单位: 米)
    maxRadius: 3000
  };

  const patientId = ref(null);
  
  const formData = reactive({
    name: '',
    id_num: '',
    phone: '',
    base_address: '', // 基础地址（地图选择）
    detail_address: '', // 详细地址（手动输入）
    latitude: '', // 纬度
    longitude: '' // 经度
  });
  
  const errors = reactive({});

  onLoad(async (options) => {
    if (options.id) {
      patientId.value = options.id;
      uni.setNavigationBarTitle({
        title: '编辑患者信息'
      });
      await fetchPatientDetails(options.id);
    } else {
      uni.setNavigationBarTitle({
        title: '添加新患者'
      });
    }
  });

  const fetchPatientDetails = async (id) => {
    const token = uni.getStorageSync('token');
    try {
      const response = await uni.request({
        url: `${API_BASE_URL}/patients/${id}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.statusCode === 200) {
        const patientData = response.data;

        // Handle backward compatibility: if old address format exists, migrate to new format
        if (patientData.address && !patientData.base_address) {
          patientData.base_address = patientData.address;
          patientData.detail_address = '';
          delete patientData.address;
        }

        Object.assign(formData, patientData);
      } else {
        uni.showToast({
          title: '获取患者信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  };

  const validateField = (field, value) => {
    errors[field] = '';
    if (!value) {
      errors[field] = '此项不能为空';
      return false;
    }
    if (field === 'id_num' && !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
      errors[field] = '身份证号格式不正确';
      return false;
    }
    if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
      errors[field] = '手机号格式不正确';
      return false;
    }
    return true;
  };

  const submit = async () => {
    // 分别验证基础地址和详细地址
    const fieldsToValidate = ['name', 'id_num', 'phone', 'base_address', 'detail_address'];
    
    fieldsToValidate.forEach(key => {
      validateField(key, formData[key]);
    });

    // 检查位置是否在范围内（如果已经选择了位置）
    if (formData.latitude && formData.longitude) {
      const isInRange = checkLocationInRange(formData.latitude, formData.longitude);
      if (!isInRange) {
        showError(`选择的位置超出了服务范围（${RANGE_CONFIG.maxRadius}米）`);
        return;
      }
    }

    if (Object.values(errors).some(error => error !== '')) return;

    const token = uni.getStorageSync('token');
    const isUpdate = !!patientId.value;
    const url = isUpdate ? `${API_BASE_URL}/patients/${patientId.value}` : `${API_BASE_URL}/patients/`;
    const method = isUpdate ? 'PUT' : 'POST';

    try {
      const response = await uni.request({
        url,
        method,
        data: formData,
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.statusCode === 200) {
        uni.showToast({
          title: isUpdate ? '更新成功' : '添加成功',
          icon: 'success'
        });
        uni.navigateBack();
      } else {
        uni.showToast({
          title: response.data.detail || (isUpdate ? '更新失败' : '添加失败'),
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  };

  // 地址选择函数
  const chooseLocation = async () => {
    try {
      // 检查权限状态
      const authStatus = await checkLocationAuth();
      if (!authStatus) {
        return;
      }

      const currentLocation = await getCurrentLocation();
      currentLocation.latitude = RANGE_CONFIG.center.latitude;
      currentLocation.longitude = RANGE_CONFIG.center.longitude;
      console.log('currentLocation:', currentLocation);
      
      // 打开位置选择
      const result = await openLocationPicker(currentLocation);
      if (result) {

        // 检查选择的位置是否在范围内
        const isInRange = checkLocationInRange(result.latitude, result.longitude);
        
        if (!isInRange) {
          showError(`选择的位置超出了服务范围（${RANGE_CONFIG.maxRadius}米）`);
          return;
        }

        // 保存基础地址和坐标
        formData.base_address = result.address + (result.name ? ` ${result.name}` : '');
        formData.latitude = result.latitude;
        formData.longitude = result.longitude;
        
        // 清除错误提示
        validateField('base_address', formData.base_address);
        showSuccess('位置选择成功');
      }
    } catch (error) {
      console.error('选择位置失败:', error);
      handleLocationError(error);
    }
  };

  // 获取用户当前位置
  const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude
          });
        },
        fail: (err) => {
          console.log('获取当前位置失败:', err);
          resolve({
            latitude: RANGE_CONFIG.center.latitude,
            longitude: RANGE_CONFIG.center.longitude
          });
          reject(err);
        }
      });
    });
  };

  // 检查位置是否在范围内
  const checkLocationInRange = (latitude, longitude) => {
    const distance = calculateDistance(
      latitude,
      longitude,
      RANGE_CONFIG.center.latitude,
      RANGE_CONFIG.center.longitude
    );
    
    console.log(`距离中心点: ${distance.toFixed(2)} `);
    return distance <= RANGE_CONFIG.maxRadius;
  };

  // 计算两个坐标点之间的距离 (使用Haversine公式)
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371000; // 地球半径，单位米
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  };

  // 检查位置权限
  const checkLocationAuth = () => {
    return new Promise((resolve) => {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          console.log('位置权限已授权');
          resolve(true);
        },
        fail: (err) => {
          console.log('位置权限未授权:', err);
          showAuthModal();
          resolve(false);
        }
      });
    });
  };

  // 显示权限申请弹窗
  const showAuthModal = () => {
    uni.showModal({
      title: '位置权限申请',
      content: '需要您的位置权限来使用地图选点功能，是否前往设置开启？',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          openSetting();
        }
      }
    });
  };

  // 打开设置页面
  const openSetting = () => {
    uni.openSetting({
      success: (res) => {
        console.log('设置页面结果:', res);
        if (res.authSetting['scope.userLocation']) {
          showSuccess('权限已开启，请重新选择位置');
        }
      }
    });
  };

  // 打开位置选择器
  const openLocationPicker = (currentLocation = null) => {
    return new Promise((resolve, reject) => {
      uni.chooseLocation({
        latitude: currentLocation ? currentLocation.latitude : null,
        longitude: currentLocation ? currentLocation.longitude : null,
        success: (res) => {
          console.log('选择位置成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('选择位置失败:', err);
          reject(err);
        }
      });
    });
  };

  // 位置选择错误处理
  const handleLocationError = (error) => {
    let errorMsg = '选择位置失败';
    
    if (error && error.errMsg) {
      if (error.errMsg.includes('auth deny') || error.errMsg.includes('权限')) {
        errorMsg = '位置权限被拒绝，请手动开启权限';
      } else if (error.errMsg.includes('cancel')) {
        errorMsg = '用户取消选择';
        return; // 用户取消不需要提示
      } else if (error.errMsg.includes('fail')) {
        errorMsg = '位置服务不可用，请检查网络或稍后重试';
      }
    }
    
    showError(errorMsg);
  };

  // 显示成功提示
  const showSuccess = (message) => {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  };

  // 显示错误提示
  const showError = (message) => {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  };
</script>

<style scoped>
  .form-container {
    padding: 15px;
    background-color: #fff;
  }

  .form-item {
    margin-bottom: 15px;
  }

  .label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .input {
    width: 100%;
    height: 60rpx;
    padding: 0 20rpx;
    background-color: #f7f7f7;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    box-sizing: border-box;
  }

  .error {
    color: red;
    font-size: 12px;
    margin-top: 5px;
  }

  .input-tip {
    display: block;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }

  /* 地址选择器样式 */
  .location-selector {
    padding: 20rpx;
    background-color: #f7f7f7;
    border-radius: 8rpx;
    border: 1px solid #e0e0e0;
  }

  .location-text {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  .location-tip {
    font-size: 24rpx;
    color: #999;
  }

  /* 地址预览样式 */
  .address-preview {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border-left: 4rpx solid #007AFF;
  }

  .preview-label {
    display: block;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
    font-weight: bold;
  }

  .preview-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
  }
</style>