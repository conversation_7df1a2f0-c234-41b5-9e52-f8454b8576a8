<template>
  <view class="patient-management-container">
    <view v-if="loading" class="loading">加载中...</view>
    <view v-else-if="patients.length === 0" class="empty-state">
      <text>暂无患者信息，请添加</text>
    </view>
    <view v-else>
      <uni-list>
        <uni-list-item v-for="patient in patients" :key="patient.id" :title="patient.name"
          :note="`地址: ${getFullAddress(patient)}`" clickable>
          <template v-slot:footer>
            <view class="patient-actions">
              <button size="mini" @click.stop="editPatient(patient.id)">编辑</button>
              <button size="mini" type="warn" @click.stop="confirmDelete(patient.id)">删除</button>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>

    <view class="add-patient-button-container">
      <button type="primary" @click="addPatient">添加新患者</button>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import { API_BASE_URL } from '@/utils/config.js';

  const patients = ref([]);
  const loading = ref(true);

  const fetchPatients = async () => {
    loading.value = true;
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      uni.reLaunch({
        url: '/pages/profile/profile'
      });
      return;
    }

    try {
      const response = await uni.request({
        url: `${API_BASE_URL}/patients/`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.statusCode === 200) {
        patients.value = response.data;
      } else {
        uni.showToast({
          title: '获取患者列表失败',
          icon: 'none'
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      loading.value = false;
    }
  };

  onShow(() => {
    fetchPatients();
  });

  const addPatient = () => {
    uni.navigateTo({
      url: '/pages/patient/form'
    });
  };

  const editPatient = (id) => {
    uni.navigateTo({
      url: `/pages/patient/form?id=${id}`
    });
  };

  const confirmDelete = (id) => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除该患者信息吗？此操作不可撤销。',
      success: (res) => {
        if (res.confirm) {
          deletePatient(id);
        }
      }
    });
  };

  const deletePatient = async (id) => {
    const token = uni.getStorageSync('token');
    try {
      const response = await uni.request({
        url: `${API_BASE_URL}/patients/${id}`,
        method: 'DELETE',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.statusCode === 204) {
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        fetchPatients(); // Refresh the list
      } else {
        const errorMsg = response.data.detail || '删除失败';
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  };

  // Helper function to combine base_address and detail_address
  const getFullAddress = (patient) => {
    if (!patient) return '';

    // Handle both old format (address) and new format (base_address + detail_address)
    if (patient.address) {
      return patient.address; // Backward compatibility
    }

    const baseAddr = patient.base_address || '';
    const detailAddr = patient.detail_address || '';

    if (baseAddr && detailAddr) {
      return `${baseAddr} ${detailAddr}`;
    } else if (baseAddr) {
      return baseAddr;
    } else if (detailAddr) {
      return detailAddr;
    }

    return '地址未设置';
  };
</script>

<style scoped>
  .patient-management-container {
    padding-bottom: 80px;
  }

  .empty-state,
  .loading {
    text-align: center;
    padding: 50px 20px;
    color: #888;
  }

  .add-patient-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 15px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
  }

  .patient-actions {
    display: flex;
    gap: 10px;
    align-items: center;
  }
</style>