<template>
  <view class="customer-container">
    <view class="profile-header">
      <!-- Avatar selection button -->
      <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
        <image class="avatar" :src="avatarUrl"></image>
      </button> 
      
      <!-- Nickname input -->
      <input 
        type="nickname" 
        class="weui-input nickname-input" 
        placeholder="请输入昵称"
        v-model="nickname"
        @blur="saveNickname"
        @confirm="saveNickname"
      />
    </view>
    
    <view class="menu-list">
      <view class="menu-item" @click="gotoPatient">
        <view>患者信息</view>
        <view class="arrow" v-html="'>'"></view>
      </view>
      <view class="menu-item" @click="contactSupport">
        <view>联系客服</view>
        <view class="arrow" v-html="'>'"></view>
      </view>
    </view>
    
    <LogoutButton />
    
  </view>
</template>

<script setup>
import LogoutButton from '@/components/LogoutButton.vue';
import { ref, onMounted } from 'vue';
import { API_BASE_URL } from '@/utils/config.js';

// Reactive data
const avatarUrl = ref('/static/icon/profile.png');
const nickname = ref('');

// Load saved data on component mount
onMounted(() => {
  loadUserProfile();
});

// Load user profile from storage
const loadUserProfile = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo) {
      avatarUrl.value = userInfo.avatarUrl || '/static/icon/profile.png';
      nickname.value = userInfo.nickname || '';
    }
  } catch (error) {
    console.error('Failed to load user profile:', error);
  }
};

// Update user profile on the server
const updateUserProfileOnServer = (data) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.showToast({ title: '请先登录', icon: 'none' });
      return reject('Not logged in');
    }

    uni.request({
      url: `${API_BASE_URL}/users/me`,
      method: 'PATCH',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: data,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
};

// Handle avatar selection
const onChooseAvatar = async (res) => {
  if (res.detail.avatarUrl) {
    const newAvatarUrl = res.detail.avatarUrl;
    try {
      await updateUserProfileOnServer({ avatar: newAvatarUrl });

      // Update local state and storage
      avatarUrl.value = newAvatarUrl;
      const userInfo = uni.getStorageSync('userInfo') || {};
      userInfo.avatarUrl = newAvatarUrl;
      uni.setStorageSync('userInfo', userInfo);
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('Failed to update avatar:', error);
      uni.showToast({
        title: '头像更新失败',
        icon: 'none'
      });
    }
  }
};

// Save nickname
const saveNickname = async () => {
  const trimmedNickname = nickname.value.trim();
  if (trimmedNickname) {
    try {
      await updateUserProfileOnServer({ nickname: trimmedNickname });

      // Update local state and storage
      const userInfo = uni.getStorageSync('userInfo') || {};
      userInfo.nickname = trimmedNickname;
      uni.setStorageSync('userInfo', userInfo);

      uni.showToast({
        title: '昵称保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('Failed to save nickname:', error);
      uni.showToast({
        title: '昵称保存失败',
        icon: 'none'
      });
    }
  }
};

// Get current user data (you can call this from other components)
const getUserProfile = () => {
  return {
    avatar: avatarUrl.value,
    nickname: nickname.value
  };
};

// Navigation functions
const gotoPatient = () => {
  uni.navigateTo({
    url: '/pages/patient/patient'
  });
};

const contactSupport = () => {
  uni.showToast({
    title: '功能暂未开放',
    icon: 'none'
  });
};

// Export functions for use in other components
defineExpose({
  getUserProfile,
  avatarUrl,
  nickname
});
</script>

<style scoped>
.customer-container {
  background-color: #f4f4f4;
  min-height: 100vh;
}

.profile-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 15px 20px;
  background-color: #fff;
}

.avatar-wrapper {
  background: none;
  border: none;
  padding: 0;
  margin-right: 10px;
  border-radius: 50%;
  overflow: hidden;
  width: 40px;
  height: 40px;
  position: relative;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

.nickname-input {
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  padding: 6px 12px;
  width: 120px;
}

.menu-list {
  margin-top: 10px;
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0; 
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f8f8f8;
}

.arrow {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}
</style>