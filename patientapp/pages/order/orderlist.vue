<template>
  <view class="order-container">
    <!-- Status Filter Tabs -->
    <view class="status-tabs">
      <text
        v-for="tab in statusTabs"
        :key="tab.status"
        class="tab-item"
        :class="{ 'active': currentStatus === tab.status }"
        @click="switchStatus(tab.status)">
        {{ tab.label }}
      </text>
    </view>

    <view v-if="loading" class="loading-state">
      <uni-load-more status="loading"></uni-load-more>
    </view>
    <view v-else-if="orders.length === 0" class="empty-state">
      <image src="/static/icon/order.png" class="empty-image"></image>
      <text class="empty-text">
        {{ currentStatus ? `暂无${getStatusLabel(currentStatus)}订单` : '您还没有任何订单' }}
      </text>
      <button class="go-home-btn" @click="goToHome">去首页逛逛</button>
    </view>
    <view v-else class="order-list">
      <view class="order-card" v-for="order in orders" :key="order.id">
        <view class="card-header">
          <text class="order-num">订单号: {{ order.order_num }}</text>
          <text class="order-status" :class="`status-${getStatusClass(order.status)}`">
            {{ getStatusLabel(order.status) }}
          </text>
        </view>
        
        <view class="card-body" @click="goToOrderDetail(order.id)">
          <view class="service-info">
            <text class="service-name">{{ order.service.name }}</text>
            <text class="patient-name">就诊人: {{ order.patient.name }}</text>
          </view>
          <text class="order-time">{{ formatDate(order.created_at) }}</text>
        </view>
        
        <view class="card-footer">
          <text class="order-amount">实付款: ￥{{ parseFloat(order.amount).toFixed(2) }}</text>
          <view class="action-buttons">
            <!-- Show pay button for unpaid orders -->
            <button 
              v-if="order.status === ORDER_STATUS.UNPAID || order.status === ORDER_STATUS.FAILED"
              class="pay-button"
              @click="handleRetryPayment(order)"
              :loading="order.paying"
            >
              {{ order.paying ? '支付中...' : (order.status === ORDER_STATUS.UNPAID ? '立即支付' : '重新支付') }}
            </button>
            
            <!-- Show cancel button for unpaid orders -->
            <button 
              v-if="[ORDER_STATUS.UNPAID, ORDER_STATUS.FAILED, ORDER_STATUS.EXPIRED].includes(order.status)"
              class="cancel-button"
              @click="handleCancelOrder(order)"
              :loading="order.cancelling"
            >
              {{ order.cancelling ? '取消中...' : '取消订单' }}
            </button>
            
            <!-- Show service button for confirmed orders -->
            <button 
              v-if="order.status === ORDER_STATUS.PAID"
              class="service-button"
              @click="contactService"
            >
              联系客服
            </button>
            
            <!-- Show delete button for terminal status orders -->
            <button
              v-if="[ORDER_STATUS.COMPLETED, ORDER_STATUS.CANCELED, ORDER_STATUS.REFUNDED].includes(order.status)"
              class="delete-button"
              @click="handleDeleteOrder(order)"
              :loading="order.deleting"
            >
              {{ order.deleting ? '删除中...' : '删除订单' }}
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- Pull to refresh and load more -->
    <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <text>点击加载更多</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onShow, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';
import { ORDER_STATUS, getStatusClass, getStatusLabel } from '@/utils/constants.js';
import { orderActions } from '@/utils/orderActions.js';

// Use the order actions composable
const {
  retryPayment,
  cancelOrder,
  deleteOrder,
  contactService
} = orderActions();

const orders = ref([]);
const loading = ref(true);
const currentStatus = ref(null);
const hasMore = ref(true);
const currentPage = ref(0);
const pageSize = 20;

const statusTabs = [
  { label: '全部', status: null },
  { label: getStatusLabel(ORDER_STATUS.UNPAID), status: ORDER_STATUS.UNPAID },
  { label: getStatusLabel(ORDER_STATUS.PAID), status: ORDER_STATUS.PAID },
  { label: getStatusLabel(ORDER_STATUS.COMPLETED), status: ORDER_STATUS.COMPLETED },
  { label: getStatusLabel(ORDER_STATUS.REFUNDING), status: ORDER_STATUS.REFUNDING },
];

onShow(() => {
  refreshOrders();
});

onPullDownRefresh(() => {
  refreshOrders();
});

onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    loadMore();
  }
});

const refreshOrders = () => {
  currentPage.value = 0;
  hasMore.value = true;
  orders.value = [];
  fetchOrders();
};

const fetchOrders = () => {
  const token = uni.getStorageSync('token');
  if (!token) {
    loading.value = false;
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }
  
  loading.value = true;
  
  let url = `${API_BASE_URL}/orders/?skip=${currentPage.value * pageSize}&limit=${pageSize}`;
  if (currentStatus.value) {
    url += `&status=${encodeURIComponent(currentStatus.value)}`;
  }
  
  uni.request({
    url: url,
    method: 'GET',
    header: { 'Authorization': `Bearer ${token}` },
    success: (res) => {
      if (res.statusCode === 200) {
        // Backend returns { orders: [...], total: number }
        const ordersData = res.data.orders || [];
        const newOrders = ordersData.map(order => ({
          ...order,
          paying: false,
          cancelling: false,
          deleting: false
        }));
        
        if (currentPage.value === 0) {
          orders.value = newOrders;
        } else {
          orders.value = [...orders.value, ...newOrders];
        }
        
        // Check if there are more orders
        hasMore.value = newOrders.length === pageSize;
        currentPage.value++;
      } else {
        uni.showToast({ title: '加载订单失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    },
    complete: () => {
      loading.value = false;
      uni.stopPullDownRefresh();
    }
  });
};

const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  fetchOrders();
};

const switchStatus = (status) => {
  if (currentStatus.value === status) return;
  
  currentStatus.value = status;
  refreshOrders();
};

const goToHome = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  });
};

const goToOrderDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/order/orderdetail?id=${orderId}`
  });
};

// Handler functions using the composable
const handleRetryPayment = async (order) => {
  // Set loading state for this specific order
  const orderIndex = orders.value.findIndex(o => o.id === order.id);
  
  await retryPayment(
    order, 
    () => {
      // On success callback - refresh orders
      refreshOrders();
    },
    (isLoading) => {
      // Set loading callback
      if (orderIndex !== -1 && orders.value[orderIndex]) {
        orders.value[orderIndex].paying = isLoading;
      }
    }
  );
};

const handleCancelOrder = async (order) => {
  // Set loading state for this specific order
  const orderIndex = orders.value.findIndex(o => o.id === order.id);
  
  await cancelOrder(
    order,
    () => {
      // On success callback - refresh orders
      refreshOrders();
    },
    (isLoading) => {
      // Set loading callback
      if (orderIndex !== -1 && orders.value[orderIndex]) {
        orders.value[orderIndex].cancelling = isLoading;
      }
    }
  );
};

const handleDeleteOrder = async (order) => {
    const orderIndex = orders.value.findIndex(o => o.id === order.id);

    await deleteOrder(
      order,
      () => {
        if (orderIndex !== -1) {
          orders.value.splice(orderIndex, 1);
        }
      },
      (isLoading) => {
            if (orderIndex !== -1) {
              const orderInList = orders.value.find(o => o.id === order.id);
              if (orderInList) {
                orderInList.deleting = false;
              }
            }
          }
    );
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN', { hour12: false });
};
</script>

<style scoped>
.order-container {
  background-color: #f4f4f4;
  min-height: 100vh;
}

.status-tabs {
  display: flex;
  background-color: #fff;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #007aff;
  border-bottom-color: #007aff;
  font-weight: bold;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.go-home-btn {
  background-color: #007aff;
  color: white;
  border-radius: 20px;
  padding: 8px 24px;
}

.order-list {
  padding: 10px;
}

.order-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.order-num {
  font-size: 12px;
  color: #999;
}

.order-status {
  font-size: 14px;
  font-weight: bold;
}

.status-unpaid { color: #faad14; }
.status-paid { color: #52c41a; }
.status-failed { color: #ff4d4f; }
.status-canceled { color: #999; }
.status-processing { color: #1890ff; }
.status-completed { color: #52c41a; }
.status-refunding { color: #faad14; }
.status-refunded { color: #999; }
.status-expired { color: #999; }
.status-closed { color: #999; }

.card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: bold;
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
}

.patient-name {
  font-size: 14px;
  color: #666;
}

.order-time {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-amount {
  font-size: 15px;
  font-weight: bold;
  color: #ff5050;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.pay-button {
  background-color: #ff5050;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
}

.cancel-button {
  background-color: #999;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
}

.delete-button {
  background-color: #e7e7e7;
  color: #666;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
}

.service-button {
  background-color: #007aff;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
}

.load-more {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}
</style>
