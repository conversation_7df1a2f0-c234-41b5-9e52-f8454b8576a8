<template>
  <view class="order-detail-container">
    <view v-if="loading" class="loading-state">
      <uni-load-more status="loading"></uni-load-more>
    </view>
    
    <view v-else-if="order" class="detail-content">
      <!-- Order Status Card -->
      <view class="status-card">
        <view class="status-icon" :class="`status-${getStatusClass(order.status)}`">
          <text class="status-text">{{ getStatusIcon(order.status) }}</text>
        </view>
        <view class="status-info">
          <text class="status-title">{{ getStatusLabel(order.status) }}</text>
          <text class="status-desc">{{ getStatusDescription(order.status) }}</text>
        </view>
      </view>

      <!-- Service Information -->
      <view class="info-card">
        <view class="card-title">服务信息</view>
        <view class="service-detail">
          <image 
            v-if="order.service && order.service.image_url" 
            :src="`${STATIC_BASE_URL}${order.service.image_url}`" 
            class="service-image" 
            mode="aspectFill"
          ></image>
          <view class="service-info">
            <text class="service-name">{{ order.service?.name || '服务名称' }}</text>
            <text class="service-price">￥{{ parseFloat(order.amount).toFixed(2) }}</text>
          </view>
        </view>
      </view>

      <!-- Patient Information -->
      <view class="info-card">
        <view class="card-title">就诊人信息</view>
        <view class="patient-info">
          <view class="info-row">
            <text class="info-label">姓名:</text>
            <text class="info-value">{{ order.patient?.name || '未知' }}</text>
          </view>
          <view class="info-row" v-if="order.patient?.phone">
            <text class="info-label">电话:</text>
            <text class="info-value">{{ order.patient.phone }}</text>
          </view>
          <view class="info-row" v-if="order.patient?.id_card">
            <text class="info-label">身份证:</text>
            <text class="info-value">{{ maskIdCard(order.patient.id_card) }}</text>
          </view>
          <view class="info-row" v-if="getPatientAddress(order.patient)">
            <text class="info-label">地址:</text>
            <text class="info-value">{{ getPatientAddress(order.patient) }}</text>
          </view>
        </view>
      </view>

      <!-- Order Information -->
      <view class="info-card">
        <view class="card-title">订单信息</view>
        <view class="order-info">
          <view class="info-row">
            <text class="info-label">订单号:</text>
            <text class="info-value selectable">{{ order.order_num }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">创建时间:</text>
            <text class="info-value">{{ formatDate(order.created_at) }}</text>
          </view>
          <view class="info-row" v-if="order.service_date && order.service_time">
            <text class="info-label">预约时间:</text>
            <text class="info-value">{{ order.service_date }} {{ order.service_time }}</text>
          </view>
          <view class="info-row" v-if="order.paid_at">
            <text class="info-label">支付时间:</text>
            <text class="info-value">{{ formatDate(order.paid_at) }}</text>
          </view>
          <view class="info-row" v-if="order.completed_at">
            <text class="info-label">完成时间:</text>
            <text class="info-value">{{ formatDate(order.completed_at) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">支付方式:</text>
            <text class="info-value">微信支付</text>
          </view>
          <view class="info-row" v-if="order.transaction_id">
            <text class="info-label">交易号:</text>
            <text class="info-value selectable">{{ order.transaction_id }}</text>
          </view>
        </view>
      </view>

      <!-- Payment Details -->
      <view class="info-card">
        <view class="card-title">费用明细</view>
        <view class="payment-details">
          <view class="detail-row">
            <text class="detail-label">服务费用</text>
            <text class="detail-value">￥{{ parseFloat(order.amount).toFixed(2) }}</text>
          </view>
          <view class="detail-row total-row">
            <text class="detail-label">实付金额</text>
            <text class="detail-value total-amount">￥{{ parseFloat(order.amount).toFixed(2) }}</text>
          </view>
        </view>
      </view>

      <!-- Action Buttons -->
      <view class="action-section" v-if="showActions">
        <button 
          v-if="[ORDER_STATUS.UNPAID, ORDER_STATUS.FAILED, ORDER_STATUS.EXPIRED].includes(order.status)"
          class="secondary-button"
          @click="handleCancelOrder"
          :loading="isCancelling"
        >
          {{ isCancelling ? '取消中...' : '取消订单' }}
        </button>
        
        <button 
          v-if="order.status === ORDER_STATUS.UNPAID || order.status === ORDER_STATUS.FAILED"
          class="primary-button"
          @click="handleRetryPayment"
          :loading="isPaymentProcessing"
        >
          {{ isPaymentProcessing ? '支付中...' : (order.status === ORDER_STATUS.UNPAID ? '立即支付' : '重新支付') }}
        </button>
        
        <button 
          v-if="order.status === ORDER_STATUS.PAID"
          class="secondary-button"
          @click="contactService"
        >
          联系客服
        </button>
        
        <button
          v-if="order.status === ORDER_STATUS.PAID && canRequestRefund"
          class="secondary-button"
          @click="handleRequestRefund"
          :loading="isRefunding"
        >
          {{ isRefunding ? '处理中...' : '申请退款' }}
        </button>
        
        <button 
          v-if="order.status === ORDER_STATUS.COMPLETED"
          class="secondary-button"
          @click="handleOrderAgain"
        >
          再次预约
        </button>
        
        <button 
          v-if="[ORDER_STATUS.COMPLETED, ORDER_STATUS.CANCELED, ORDER_STATUS.REFUNDED].includes(order.status)"
          class="secondary-button delete-button"
          @click="handleDeleteOrder"
          :loading="isDeleting"
        >
          {{ isDeleting ? '删除中...' : '删除订单' }}
        </button>
      </view>
    </view>

    <view v-else class="error-state">
      <image src="/static/icon/error.png" class="error-image"></image>
      <text class="error-text">订单不存在或已被删除</text>
      <button class="back-button" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';
import { 
  ORDER_STATUS, 
  getStatusClass, 
  getStatusIcon, 
  getStatusDescription,
  getStatusLabel
} from '@/utils/constants.js';
import { orderActions } from '@/utils/orderActions.js';

// Use the order actions composable
const {
  isPaymentProcessing,
  isCancelling,
  retryPayment,
  cancelOrder,
  deleteOrder,
  contactService,
  orderAgain
} = orderActions();

const order = ref(null);
const orderId = ref(null);
const loading = ref(true);
const isDeleting = ref(false);
const isRefunding = ref(false);

const showActions = computed(() => {
  if (!order.value) return false;
  return [
    ORDER_STATUS.UNPAID,
    ORDER_STATUS.PAID,
    ORDER_STATUS.FAILED,
    ORDER_STATUS.EXPIRED,
    ORDER_STATUS.COMPLETED,
    ORDER_STATUS.CANCELED,
    ORDER_STATUS.REFUNDED
  ].includes(order.value.status);
});

const canRequestRefund = computed(() => {
  if (!order.value || !order.value.service_date || !order.value.service_time) {
    return false;
  }

  // Combine date and time to create a full ISO-like string for reliable parsing
  const serviceDateTimeString = `${order.value.service_date}T${order.value.service_time}:00`;
  const serviceDateTime = new Date(serviceDateTimeString);

  // Check for invalid date, which can happen with bad data
  if (isNaN(serviceDateTime.getTime())) {
    return false;
  }

  const now = new Date();
  const twentyFourHoursInMillis = 24 * 60 * 60 * 1000;
  
  // The service time must be more than 24 hours from the current time.
  return (serviceDateTime.getTime() - now.getTime()) > twentyFourHoursInMillis;
});

// Auto-refresh timer for checking payment status
let statusCheckTimer = null;

onLoad((options) => {
  if (options.id) {
    orderId.value = options.id;
    fetchOrderDetail();
    
    // Start auto-refresh for unpaid orders
    startStatusCheck();
  } else {
    loading.value = false;
  }
});

const startStatusCheck = () => {
  // Clear existing timer
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer);
  }
  
  // Auto-refresh every 60 seconds for unpaid orders
  statusCheckTimer = setInterval(() => {
    if (order.value && order.value.status === ORDER_STATUS.UNPAID) {
      fetchOrderDetail(false); // Silent refresh
    } else {
      clearInterval(statusCheckTimer);
    }
  }, 60000);
};

const fetchOrderDetail = (showLoading = true) => {
  const token = uni.getStorageSync('token');
  if (!token) {
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }
  
  if (showLoading) {
    loading.value = true;
  }
  
  uni.request({
    url: `${API_BASE_URL}/orders/${orderId.value}`,
    method: 'GET',
    header: {
      'Authorization': `Bearer ${token}`
    },
    success: (res) => {
      if (res.statusCode === 200) {
        const oldStatus = order.value?.status;
        order.value = res.data;
        
        // Show status change notification
        if (oldStatus && oldStatus !== res.data.status) {
          uni.showToast({ 
            title: `订单状态已更新: ${getStatusLabel(res.data.status)}`, 
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        uni.showToast({ title: '获取订单详情失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    },
    complete: () => {
      if (showLoading) {
        loading.value = false;
      }
    }
  });
};

// Handler functions using the composable
const handleRetryPayment = async () => {
  if (!order.value || (order.value.status !== ORDER_STATUS.UNPAID && order.value.status !== ORDER_STATUS.FAILED)) {
    uni.showToast({ title: '订单状态异常', icon: 'none' });
    return;
  }
  
  // Show confirmation dialog
  uni.showModal({
    title: '重新支付',
    content: '重新支付订单，是否继续？',
    success: async (res) => {
      if (res.confirm) {
        await retryPayment(order.value, () => {
          fetchOrderDetail();
          startStatusCheck();
        });
      }
    }
  });
};

const handleRequestRefund = () => {
  uni.showModal({
    title: '申请退款',
    content: '您确定要申请退款吗？此操作将提交退款请求，等待后台审核。',
    success: async (res) => {
      if (res.confirm) {
        isRefunding.value = true;
        const token = uni.getStorageSync('token');
        try {
          const response = await new Promise((resolve, reject) => {
            uni.request({
              url: `${API_BASE_URL}/orders/${order.value.id}`,
              method: 'PATCH',
              header: { 
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              data: { status: ORDER_STATUS.REFUNDING },
              success: resolve,
              fail: reject,
            });
          });

          if (response.statusCode === 200) {
            uni.showToast({ title: '退款申请已提交', icon: 'success' });
            fetchOrderDetail(); // Refresh details
          } else {
            uni.showToast({ title: response.data?.detail || '申请失败', icon: 'none' });
          }
        } catch (error) {
          uni.showToast({ title: '网络错误', icon: 'none' });
        } finally {
          isRefunding.value = false;
        }
      }
    }
  });
};

const handleCancelOrder = async () => {
  await cancelOrder(order.value, () => {
    fetchOrderDetail(); // Refresh order details
  });
};

const handleOrderAgain = () => {
  orderAgain(order.value);
};

const handleDeleteOrder = async () => {
  await deleteOrder(
    order.value,
    () => uni.navigateBack(),
    (isLoading) => {
      isDeleting.value = isLoading;
    }
  );
};

const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN', { hour12: false });
};

const maskIdCard = (idCard) => {
  if (!idCard || idCard.length < 8) return idCard;
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
};

// Helper function to get patient's full address
const getPatientAddress = (patient) => {
  if (!patient) return '';

  // Handle both old format (address) and new format (base_address + detail_address)
  if (patient.address) {
    return patient.address; // Backward compatibility
  }

  const baseAddr = patient.base_address || '';
  const detailAddr = patient.detail_address || '';

  if (baseAddr && detailAddr) {
    return `${baseAddr} ${detailAddr}`;
  } else if (baseAddr) {
    return baseAddr;
  } else if (detailAddr) {
    return detailAddr;
  }

  return '';
};

// Cleanup timer on component unmount
onUnmounted(() => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer);
  }
});
</script>

<style scoped>
.order-detail-container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding-bottom: 20px;
}

.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.error-image {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

.error-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.back-button {
  background-color: #007aff;
  color: white;
  border-radius: 20px;
  padding: 8px 24px;
}

.detail-content {
  padding: 10px;
}

.status-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.status-icon.status-unpaid { background-color: #fff2e8; }
.status-icon.status-paid { background-color: #f6ffed; }
.status-icon.status-failed { background-color: #fff2f0; }
.status-icon.status-canceled { background-color: #f5f5f5; }
.status-icon.status-processing { background-color: #e6f7ff; }
.status-icon.status-completed { background-color: #f6ffed; }

.status-text {
  font-size: 24px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.status-desc {
  font-size: 14px;
  color: #666;
}

.info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.service-detail {
  display: flex;
  align-items: center;
}

.service-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 15px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.service-price {
  font-size: 18px;
  color: #ff5050;
  font-weight: bold;
}

.patient-info, .order-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value.selectable {
  user-select: text;
  -webkit-user-select: text;
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-row.total-row {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  margin-top: 8px;
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

.total-amount {
  font-size: 16px;
  font-weight: bold;
  color: #ff5050;
}

.action-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.primary-button {
  background-color: #ff5050;
  color: white;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  font-weight: bold;
}

.secondary-button {
  background-color: #f8f8f8;
  color: #666;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  border: 1px solid #e0e0e0;
}

.secondary-button.delete-button {
  color: #ff5050;
  border-color: #ffb8b8;
}

.primary-button:disabled {
  background-color: #ccc;
  color: #999;
}

.secondary-button:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #f0f0f0;
}
</style>
