<template>
  <view class="map-container">
    <map 
      id="restrictedMap"
      :latitude="centerLat"
      :longitude="centerLng"
      :markers="markers"
      :polygons="polygons"
      :scale="scale"
      :include-points="includePoints"
      @regionchange="onRegionChange"
      @tap="onMapTap"
      style="width: 100%; height: 70vh;"
      show-location
    >
    </map>
    
    <view class="bottom-area">
      <!-- 选择的地点信息 -->
      <view class="selected-location-info">
        <text class="info-title">选择的地点:</text>
        <text class="location-address">{{ selectedAddress || '尚未选择地点' }}</text>
        <view class="location-coordinates" v-if="selectedLocation.latitude">
          <text class="coordinate">纬度: {{ selectedLocation.latitude.toFixed(6) }}</text>
          <text class="coordinate">经度: {{ selectedLocation.longitude.toFixed(6) }}</text>
        </view>
      </view>
      
      <view class="range-tip" :class="{'out-of-range': isOutOfRange}">
        {{ rangeTip }}
      </view>
      
      <view class="button-group">
        <button class="btn" @tap="getCurrentLocation">我的位置</button>
        <button class="btn confirm-btn" :disabled="isOutOfRange" @tap="confirmLocation">
          {{ isOutOfRange ? '请在范围内选择' : '确认选择' }}
        </button>
      </view>
    </view>
    
    <!-- 范围提示 -->
    <view class="range-overlay" v-if="showRangeHint">
      <text class="range-hint-text">请在地图阴影范围内选择位置</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 配置允许的范围（以西安为例）
const allowedCity = '西安市';
const centerLat = 34.259169;
const centerLng = 108.917916;

const scale = ref(12);
const markers = ref([]);
const polygons = ref([]);
const selectedAddress = ref('');
const showRangeHint = ref(true);
const isOutOfRange = ref(false);

const selectedLocation = reactive({
  latitude: 0,
  longitude: 0
});

// 定义服务范围
const serviceRange = [
  { latitude: 34.277359, longitude: 108.891179 },   // 西北角
  { latitude: 34.277363, longitude: 108.972381 },   // 东北角
  { latitude: 34.241295, longitude: 108.972359 },   // 东南角
  { latitude: 34.240533, longitude: 108.889710 }    // 西南角
];

// 地图显示范围
const includePoints = ref([...serviceRange]);

// 计算提示信息
const rangeTip = computed(() => {
  return isOutOfRange.value ? 
    '当前位置超出服务范围，请重新选择' : 
    `选择范围：${allowedCity}`;
});

onMounted(() => {
  initializeMap();
  getCurrentLocation();
});

const initializeMap = () => {
  // 创建范围多边形
  polygons.value = [{
    points: serviceRange,
    strokeWidth: 2,
    strokeColor: '#007AFF',
    strokeStyle: 'dashed', // 虚线边框
    fillColor: 'rgba(0, 122, 255, 0.1)' // 半透明填充
  }];
};

const getCurrentLocation = () => {
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      const inRange = checkCoordinateRange(res.latitude, res.longitude);
      updateMapCenter(res.latitude, res.longitude, inRange);
      
      if (!inRange) {
        uni.showToast({
          title: '当前位置不在服务范围',
          icon: 'none'
        });
      }
    },
    fail: () => {
      updateMapCenter(centerLat, centerLng, true);
    }
  });
};

const updateMapCenter = (lat, lng, inRange = true) => {
  selectedLocation.latitude = lat;
  selectedLocation.longitude = lng;
  isOutOfRange.value = !inRange;
  updateMarkers();
  // 暂时屏蔽逆地理编码
  generateSimpleAddress(lat, lng);
};

const onMapTap = (e) => {
  const { latitude, longitude } = e.detail;
  
  const inRange = checkCoordinateRange(latitude, longitude);
  isOutOfRange.value = !inRange;
  
  if (!inRange) {
    uni.showToast({
      title: '请在地图阴影范围内选择',
      icon: 'none'
    });
    return;
  }
  
  selectedLocation.latitude = latitude;
  selectedLocation.longitude = longitude;
  updateMarkers();
  generateSimpleAddress(latitude, longitude);
};

const checkCoordinateRange = (lat, lng) => {
  // 简单矩形范围检查
  const minLat = Math.min(...serviceRange.map(p => p.latitude));
  const maxLat = Math.max(...serviceRange.map(p => p.latitude));
  const minLng = Math.min(...serviceRange.map(p => p.longitude));
  const maxLng = Math.max(...serviceRange.map(p => p.longitude));
  
  return lat >= minLat && lat <= maxLat && lng >= minLng && lng <= maxLng;
};

const updateMarkers = () => {
  markers.value = [{
    id: 0,
    latitude: selectedLocation.latitude,
    longitude: selectedLocation.longitude,
    iconPath: isOutOfRange.value ? '/static/icon/location-marker-disabled.png' : '/static/icon/location-marker.png',
    width: 30,
    height: 30,
    title: isOutOfRange.value ? '超出范围' : '选择的位置'
  }];
};

// 生成简单地址（替代逆地理编码）
const generateSimpleAddress = (lat, lng) => {
  // 根据坐标范围生成大致位置描述
  const inRange = checkCoordinateRange(lat, lng);
  
  if (!inRange) {
    selectedAddress.value = '位置超出服务范围';
    return;
  }
  
  // 简单的位置区域判断（可以根据实际范围调整）
  const centerLat = 34.259169;
  const centerLng = 108.917916;
  
  let area = '';
  if (lat > centerLat) {
    area += '北';
  } else {
    area += '南';
  }
  
  if (lng > centerLng) {
    area += '东';
  } else {
    area += '西';
  }
  
  selectedAddress.value = `西安市${area}部区域`;
  
  // 可以添加更详细的位置描述
  const distanceFromCenter = Math.sqrt(
    Math.pow(lat - centerLat, 2) + Math.pow(lng - centerLng, 2)
  ) * 111; // 大致公里数
  
  if (distanceFromCenter < 2) {
    selectedAddress.value = '西安市核心区域';
  } else if (distanceFromCenter < 5) {
    selectedAddress.value = `西安市${area}部近郊`;
  } else {
    selectedAddress.value = `西安市${area}部远郊`;
  }
};

// 暂时屏蔽的逆地理编码函数
// const reverseGeocode = (lat, lng) => {
//   uni.request({
//     url: `https://apis.map.qq.com/ws/geocoder/v1/`,
//     data: {
//       location: `${lat},${lng}`,
//       key: '你的腾讯地图密钥'
//     },
//     success: (res) => {
//       if (res.data.status === 0) {
//         selectedAddress.value = res.data.result.address;
        
//         // 检查地址是否在允许的城市
//         const city = res.data.result.address_component.city;
//         if (!city.includes(allowedCity)) {
//           isOutOfRange.value = true;
//         }
//       }
//     },
//     fail: () => {
//       selectedAddress.value = `纬度: ${lat.toFixed(6)}, 经度: ${lng.toFixed(6)}`;
//     }
//   });
// };

const confirmLocation = () => {
  if (isOutOfRange.value) {
    uni.showToast({
      title: '请选择在服务范围内的位置',
      icon: 'none'
    });
    return;
  }
  
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  
  if (prevPage) {
    const eventChannel = prevPage.getOpenerEventChannel();
    eventChannel.emit('locationSelected', {
      address: selectedAddress.value,
      latitude: selectedLocation.latitude,
      longitude: selectedLocation.longitude
    });
  }
  
  uni.navigateBack();
};

// 3秒后隐藏提示
setTimeout(() => {
  showRangeHint.value = false;
}, 3000);
</script>

<style scoped>
.map-container {
  height: 100vh;
  position: relative;
}

.bottom-area {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

/* 选择的地点信息样式 */
.selected-location-info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #007AFF;
}

.info-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.location-address {
  display: block;
  font-size: 28rpx;
  color: #007AFF;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.location-coordinates {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.coordinate {
  font-size: 24rpx;
  color: #666;
}

.range-tip {
  font-size: 24rpx;
  color: #007AFF;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 10rpx;
  background: #f0f7ff;
  border-radius: 8rpx;
}

.range-tip.out-of-range {
  color: #FF6B6B;
  background: #fff0f0;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  border-radius: 10rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:disabled {
  background-color: #CCCCCC;
  color: #666666;
}

/* 范围提示覆盖层 */
.range-overlay {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  z-index: 999;
}

.range-hint-text {
  color: white;
  font-size: 24rpx;
}
</style>