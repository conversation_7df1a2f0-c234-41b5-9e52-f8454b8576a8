
import { ref } from 'vue';
import { API_BASE_URL } from '@/utils/config.js';
import { ORDER_STATUS } from '@/utils/constants.js';

export function orderActions() {
  const isPaymentProcessing = ref(false);
  const isCancelling = ref(false);
  const isMockProcessing = ref(false);

  // Detect if this is a mock payment based on backend response
  const detectMockPayment = (paymentParams, responseData = null) => {
    // Method 1: Check backend environment response (preferred)
    if (responseData && responseData.environment) {
      return responseData.environment.is_mock === true;
    }
    
    // Method 2: Check if payment parameters contain mock indicators
    if (paymentParams.paySign && paymentParams.paySign.startsWith('mock_sign_')) {
      return true;
    }
    
    // Method 3: Check if package contains mock prepay_id
    if (paymentParams.package && paymentParams.package.includes('mock_prepay_')) {
      return true;
    }
    
    // Method 4: Check API base URL (fallback detection)
    if (API_BASE_URL.includes('localhost') || API_BASE_URL.includes('127.0.0.1') || 
        API_BASE_URL.includes(':8000') || API_BASE_URL.includes('dev.')) {
      return true;
    }
    
    // Default to real payment
    return false;
  };

  // Enhanced mock payment completion with better error handling
  const mockCompletePayment = async (order, onSuccess) => {
    if (!order || !order.order_num) {
      uni.showToast({ title: '订单信息无效', icon: 'none' });
      return;
    }
    
    // Prevent double-clicking
    if (isMockProcessing.value) {
      return;
    }
    
    isMockProcessing.value = true;
    
    try {
      const token = uni.getStorageSync('token');
      
      // Show processing state
      uni.showLoading({
        title: '模拟支付处理中...',
        mask: true
      });
      
      const res = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/mockpay/complete/${order.order_num}`,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          data: {
            order_num: order.order_num
          },
          timeout: 10000, // 10 second timeout
          success: resolve,
          fail: reject
        });
      });
      
      uni.hideLoading();
      
      if (res.statusCode === 200) {
        uni.showToast({ 
          title: '模拟支付成功', 
          icon: 'success',
          duration: 2000 
        });
        
        // Call success callback after mock success
        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 2000);
        }
        
      } else {
        throw new Error(res.data?.detail || '模拟支付失败');
      }
      
    } catch (error) {
      uni.hideLoading();
      console.error('Mock payment error:', error);
      
      // Enhanced error handling for mock payments
      if (error.errMsg && error.errMsg.includes('timeout')) {
        uni.showModal({
          title: '模拟支付超时',
          content: '模拟支付请求超时，可能是网络问题。是否重试？',
          success: (res) => {
            if (res.confirm) {
              setTimeout(() => mockCompletePayment(order, onSuccess), 1000);
            }
          }
        });
      } else {
        uni.showToast({ 
          title: error.message || '模拟支付失败', 
          icon: 'none',
          duration: 3000
        });
      }
    } finally {
      isMockProcessing.value = false;
    }
  };

  // Show mock payment options if auto-completion fails
  const showMockPaymentOptions = (orderData, order, onSuccess) => {
    uni.showModal({
      title: '模拟支付',
      content: `支付环境检测为模拟模式。

订单号: ${orderData.order_num}
支付金额: ¥${orderData.amount}

点击确定完成模拟支付。`,
      showCancel: true,
      cancelText: '取消',
      confirmText: '完成支付',
      success: (res) => {
        if (res.confirm) {
          mockCompletePayment(order, onSuccess);
        }
      }
    });
  };

  // Handle mock payment flow (when backend returns mock parameters)
  const handleMockPaymentFlow = async (orderData, order, onSuccess) => {
    try {
      console.log('Mock payment environment detected for order:', order.order_num);
      
      // Show subtle mock payment indication
      uni.showToast({
        title: '模拟支付环境',
        icon: 'none',
        duration: 2000
      });
      
      // Auto-trigger mock payment completion after a short delay
      setTimeout(async () => {
        try {
          await mockCompletePayment(order, onSuccess);
        } catch (error) {
          console.error('Auto mock payment failed:', error);
          // Fallback: show manual option
          showMockPaymentOptions(orderData, order, onSuccess);
        }
      }, 2000); // 2 second delay
      
    } catch (error) {
      console.error('Mock payment flow error:', error);
      uni.showToast({
        title: '模拟支付设置失败',
        icon: 'none'
      });
    }
  };

  // Handle payment failure scenarios
  const handlePaymentFailure = (payErr, order, retryCallback) => {
    console.log('Payment failure details:', payErr);
    
    // Analyze failure reason
    if (payErr.errMsg) {
      if (payErr.errMsg.includes('cancel')) {
        // User cancelled payment
        uni.showModal({
          title: '支付已取消',
          content: '您取消了支付，可以稍后重试',
          showCancel: false,
          confirmText: '知道了'
        });
      } else if (payErr.errMsg.includes('fail')) {
        // Payment failed
        uni.showModal({
          title: '支付失败',
          content: '支付过程中出现问题，请检查网络连接后重试',
          showCancel: false,
          confirmText: '重试',
          success: (res) => {
            if (res.confirm && retryCallback) {
              // Allow immediate retry
              setTimeout(() => {
                retryCallback(order);
              }, 1000);
            }
          }
        });
      } else {
        // Unknown error
        uni.showToast({ 
          title: '支付失败，请重试', 
          icon: 'none',
          duration: 3000
        });
      }
    } else {
      // No error message provided
      uni.showToast({ 
        title: '支付异常，请重试', 
        icon: 'none',
        duration: 3000
      });
    }
  };

  // Handle real WeChat payment flow (production environment)  
  const handleRealPaymentFlow = async (paymentParams, order, onSuccess, retryCallback) => {
    try {
      // Validate payment parameters
      if (!paymentParams.timeStamp || !paymentParams.nonceStr || 
          !paymentParams.package || !paymentParams.paySign) {
        throw new Error('支付参数不完整');
      }
      
      // Show loading state
      uni.showLoading({
        title: '正在调起支付...',
        mask: true
      });
      
      // Small delay to ensure UI updates
      await new Promise(resolve => setTimeout(resolve, 500));
      
      uni.hideLoading();
      
      // Initiate WeChat payment
      return new Promise((resolve, reject) => {
        uni.requestPayment({
          timeStamp: paymentParams.timeStamp,
          nonceStr: paymentParams.nonceStr,
          package: paymentParams.package,
          signType: paymentParams.signType || 'RSA',
          paySign: paymentParams.paySign,
          
          success: (payRes) => {
            console.log('WeChat payment success:', payRes);
            
            uni.showToast({ 
              title: '支付成功！', 
              icon: 'success',
              duration: 2000
            });
            
            // Call success callback after real payment success
            if (onSuccess) {
              setTimeout(() => {
                onSuccess();
              }, 2000);
            }
            
            resolve(payRes);
          },
          
          fail: (payErr) => {
            console.error('WeChat payment failed:', payErr);
            
            // Handle different payment failure scenarios
            handlePaymentFailure(payErr, order, retryCallback);
            reject(payErr);
          },
          
          complete: () => {
            console.log('WeChat payment completed (success or fail)');
          }
        });
      });
      
    } catch (error) {
      uni.hideLoading();
      console.error('Real payment flow error:', error);
      uni.showToast({ 
        title: error.message || '调起支付失败', 
        icon: 'none' 
      });
      throw error;
    }
  };

  // Main retry payment function
  const retryPayment = async (order, onSuccess, setLoading) => {
    if (!order || (order.status !== ORDER_STATUS.UNPAID && order.status !== ORDER_STATUS.FAILED)) {
      uni.showToast({ title: '订单状态异常', icon: 'none' });
      return;
    }
    
    // Set loading state if provided
    if (setLoading) {
      setLoading(true);
    }
    isPaymentProcessing.value = true;
    
    try {
      const token = uni.getStorageSync('token');
      
      // First, get the current order status to ensure it's still payable
      const statusRes = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/orders/${order.id}`,
          method: 'GET',
          header: { 'Authorization': `Bearer ${token}` },
          success: resolve,
          fail: reject
        });
      });
      
      if (statusRes.statusCode !== 200) {
        throw new Error('获取订单状态失败');
      }
      
      const currentOrder = statusRes.data;
      if (currentOrder.status !== ORDER_STATUS.UNPAID && currentOrder.status !== ORDER_STATUS.FAILED) {
        uni.showToast({ title: '订单状态已变更，请刷新页面', icon: 'none' });
        if (onSuccess) onSuccess();
        return;
      }
      
      // Create a new payment request for the existing unpaid order
      const payRes = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/orders/${order.id}/payments`,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (payRes.statusCode === 200 && payRes.data.payment_params) {
        const { payment_params } = payRes.data;
        
        // Detect payment environment from backend response
        const isMockPayment = detectMockPayment(payment_params, payRes.data);
        
        if (isMockPayment) {
          await handleMockPaymentFlow(payRes.data, order, onSuccess);
        } else {
          await handleRealPaymentFlow(payment_params, order, onSuccess, retryPayment);
        }
        
      } else {
        throw new Error(payRes.data?.detail || '重新支付失败');
      }
      
    } catch (error) {
      console.error('Retry payment error:', error);
      uni.showToast({ 
        title: error.message || '支付失败，请重试', 
        icon: 'none' 
      });
    } finally {
      isPaymentProcessing.value = false;
      if (setLoading) {
        setLoading(false);
      }
    }
  };

  // Cancel order function
  const cancelOrder = async (order, onSuccess, setLoading) => {
    uni.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          if (setLoading) {
            setLoading(true);
          }
          isCancelling.value = true;
          
          try {
            const token = uni.getStorageSync('token');
            
            const cancelRes = await new Promise((resolve, reject) => {
              uni.request({
                url: `${API_BASE_URL}/orders/${order.id}`,
                method: 'PATCH',
                header: { 'Authorization': `Bearer ${token}` },
                data: { status: ORDER_STATUS.CANCELED },
                success: resolve,
                fail: reject
              });
            });
            
            if (cancelRes.statusCode === 200) {
              uni.showToast({ title: '订单已取消', icon: 'success' });
              if (onSuccess) {
                setTimeout(() => {
                  onSuccess();
                }, 1500);
              }
            } else {
              throw new Error(cancelRes.data?.detail || '取消订单失败');
            }
            
          } catch (error) {
            console.error('Cancel order error:', error);
            uni.showToast({ 
              title: error.message || '取消订单失败', 
              icon: 'none' 
            });
          } finally {
            isCancelling.value = false;
            if (setLoading) {
              setLoading(false);
            }
          }
        }
      }
    });
  };

  const deleteOrder = async (order, onSuccess, setLoading) => {
    uni.showModal({
      title: '删除订单',
      content: '您确定要删除此订单吗？删除后将无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          if (setLoading) {
            setLoading(true);
          }
          try {
            const token = uni.getStorageSync('token');
            const res = await new Promise((resolve, reject) => {
              uni.request({
                url: `${API_BASE_URL}/orders/${order.id}`,
                method: 'DELETE',
                header: { 'Authorization': `Bearer ${token}` },
                success: resolve,
                fail: reject
              });
            });
            if (res.statusCode === 204) {
              uni.showToast({ title: '订单已删除', icon: 'success' });
              if (onSuccess) {
                onSuccess();
              }
            } else {
              throw new Error((res.data && res.data.detail) || '删除失败');
            }
          } catch (error) {
            uni.showToast({ title: error.message || '删除订单失败', icon: 'none' });
          } finally {
            if (setLoading) {
              setLoading(false);
            }
          }
        }
      }
    });
  };

  // Contact service function
  const contactService = () => {
    uni.showModal({
      title: '联系客服',
      content: '请拨打客服电话: 400-XXX-XXXX。工作时间: 9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  };

  // Order again function
  const orderAgain = (order) => {
    if (order && order.service) {
      uni.navigateTo({
        url: `/pages/service/ordering?id=${order.service.id}`
      });
    }
  };

  return {
    // State
    isPaymentProcessing,
    isCancelling,
    isMockProcessing,
    
    // Actions
    retryPayment,
    cancelOrder,
    deleteOrder,
    contactService,
    orderAgain,
    
    // Utility functions (in case needed for custom implementations)
    detectMockPayment,
    mockCompletePayment,
    handleMockPaymentFlow,
    handleRealPaymentFlow
  };
}
