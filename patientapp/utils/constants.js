// Order Statuses - English values for internal use (API, logic)
export const ORDER_STATUS = {
  UNPAID: 'UNPAID',
  PAID: 'PAID',
  FAILED: 'FAILED',
  CANCELED: 'CANCELED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  REFUNDING: 'REFUNDING',
  REFUNDED: 'REFUNDED',
  EXPIRED: 'EXPIRED',
  CLOSED: 'CLOSED',
  DELETED: 'DELETED',
};

// Chinese labels for UI display
export const STATUS_LABEL_MAP = {
  [ORDER_STATUS.UNPAID]: '待支付',
  [ORDER_STATUS.PAID]: '已支付',
  [ORDER_STATUS.FAILED]: '支付失败',
  [ORDER_STATUS.CANCELED]: '已取消',
  [ORDER_STATUS.PROCESSING]: '处理中',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.REFUNDING]: '退款中',
  [ORDER_STATUS.REFUNDED]: '已退款',
  [ORDER_STATUS.EXPIRED]: '已过期',
  [ORDER_STATUS.CLOSED]: '已关闭',
  [ORDER_STATUS.DELETED]: '已删除',
};

// Mapping from status string to a CSS class-friendly key
export const STATUS_CLASS_MAP = {
  [ORDER_STATUS.UNPAID]: 'unpaid',
  [ORDER_STATUS.PAID]: 'paid',
  [ORDER_STATUS.FAILED]: 'failed',
  [ORDER_STATUS.CANCELED]: 'canceled',
  [ORDER_STATUS.PROCESSING]: 'processing',
  [ORDER_STATUS.COMPLETED]: 'completed',
  [ORDER_STATUS.REFUNDING]: 'refunding',
  [ORDER_STATUS.REFUNDED]: 'refunded',
  [ORDER_STATUS.EXPIRED]: 'expired',
  [ORDER_STATUS.CLOSED]: 'closed',
  [ORDER_STATUS.DELETED]: 'deleted',
};

// Mapping from status string to an icon
export const STATUS_ICON_MAP = {
  [ORDER_STATUS.UNPAID]: '⏰', // 待支付
  [ORDER_STATUS.PAID]: '✅', // 已支付
  [ORDER_STATUS.FAILED]: '❌', // 支付失败
  [ORDER_STATUS.CANCELED]: '⭕', // 已取消
  [ORDER_STATUS.PROCESSING]: '🔄', // 处理中
  [ORDER_STATUS.COMPLETED]: '✨', // 已完成
  [ORDER_STATUS.REFUNDING]: '↩️', // 退款中
  [ORDER_STATUS.REFUNDED]: '💰', // 已退款
  [ORDER_STATUS.EXPIRED]: '⏳', // 已过期
  [ORDER_STATUS.CLOSED]: '🔒', // 已关闭
  [ORDER_STATUS.DELETED]: '🗑️', // 已删除
};

// Mapping from status string to a description
export const STATUS_DESCRIPTION_MAP = {
  [ORDER_STATUS.UNPAID]: '请尽快完成支付',
  [ORDER_STATUS.PAID]: '支付成功，等待服务',
  [ORDER_STATUS.FAILED]: '支付未成功，请重试',
  [ORDER_STATUS.CANCELED]: '订单已取消',
  [ORDER_STATUS.PROCESSING]: '服务正在进行中',
  [ORDER_STATUS.COMPLETED]: '服务已完成',
  [ORDER_STATUS.REFUNDING]: '退款正在处理中',
  [ORDER_STATUS.REFUNDED]: '退款已到账',
  [ORDER_STATUS.EXPIRED]: '订单已过期',
  [ORDER_STATUS.CLOSED]: '订单已关闭',
  [ORDER_STATUS.DELETED]: '订单已删除',
};

/**
 * Gets the Chinese label for a given order status.
 * @param {string} status - The order status string (e.g., 'UNPAID').
 * @returns {string} The corresponding Chinese label.
 */
export const getStatusLabel = (status) => {
  return STATUS_LABEL_MAP[status] || status;
};

/**
 * Gets the CSS class for a given order status.
 * @param {string} status - The order status string.
 * @returns {string} The corresponding CSS class.
 */
export const getStatusClass = (status) => {
  return STATUS_CLASS_MAP[status] || 'unknown';
};

/**
 * Gets the icon for a given order status.
 * @param {string} status - The order status string.
 * @returns {string} The corresponding icon.
 */
export const getStatusIcon = (status) => {
  return STATUS_ICON_MAP[status] || '❓';
};

/**
 * Gets the description for a given order status.
 * @param {string} status - The order status string.
 * @returns {string} The corresponding description.
 */
export const getStatusDescription = (status) => {
  return STATUS_DESCRIPTION_MAP[status] || '未知状态';
};