{"id": "uni-drawer", "displayName": "uni-drawer 抽屉", "version": "1.2.1", "description": "抽屉式导航，用于展示侧滑菜单，侧滑导航。", "keywords": ["uni-ui", "uniui", "drawer", "抽屉", "侧滑导航"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": "", "uni-app": "^3.1.0", "uni-app-x": "^3.1.0"}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue", "darkmode": "-", "i18n": "-", "widescreen": "-"}, "uni_modules": {"dependencies": ["uni-scss"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√"}, "client": {"uni-app": {"vue": {"vue2": "-", "vue3": "-"}, "web": {"safari": "-", "chrome": "-"}, "app": {"vue": "-", "nvue": "-", "android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}